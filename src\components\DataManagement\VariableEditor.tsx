import React, { useState, useMemo } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  IconButton,
  Button,
  Tooltip,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  SelectChangeEvent,
  Alert,
  Stack,
  Card,
  CardContent,
  CardHeader,
  Avatar,
  useTheme,
  Container,
  InputAdornment,
  alpha,
  Grid,
  Divider,
  ButtonGroup,
  Badge,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Collapse,
  FormHelperText,
  ToggleButton,
  ToggleButtonGroup,
  Menu,
  Checkbox,
  LinearProgress,
  useMediaQuery
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  TableView as TableViewIcon,
  Fullscreen as FullscreenIcon,
  FullscreenExit as FullscreenExitIcon,
  Search as SearchIcon,
  Clear as ClearIcon,
  Numbers as NumbersIcon,
  TextFields as TextFieldsIcon,
  ToggleOn as ToggleOnIcon,
  CalendarToday as CalendarIcon,
  Category as CategoryIcon,
  Dataset as DatasetIcon,
  Functions as FunctionsIcon,
  Science as ScienceIcon,
  Psychology as PsychologyIcon,
  TrendingUp as TrendingUpIcon,
  ViewList as ViewListIcon,
  ViewModule as ViewModuleIcon,
  FilterList as FilterListIcon,
  MoreVert as MoreVertIcon,
  Info as InfoIcon,
  Warning as WarningIcon,
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon,
  DragIndicator as DragIndicatorIcon,
  ContentCopy as ContentCopyIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  SignalCellularAlt as SignalCellularAltIcon, // Added for ordered bars icon
  KeyboardArrowUp as ArrowUpIcon,
  KeyboardArrowDown as ArrowDownIcon,
  Add as AddCategoryIcon,
  Delete as DeleteCategoryIcon
} from '@mui/icons-material';
import { useData } from '../../context/DataContext';
import { DataType, VariableRole, Dataset } from '../../types';
import { DatasetSelector } from '../UI';

interface VariableEditorProps {
  onGoToDataEditor?: (datasetId: string) => void;
}

const VariableEditor: React.FC<VariableEditorProps> = ({ onGoToDataEditor }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { 
    datasets, 
    currentDataset, 
    setCurrentDataset, 
    addColumn,
    updateColumn,
    removeColumn
  } = useData();
  
  const [selectedDatasetId, setSelectedDatasetId] = useState<string | null>(currentDataset?.id || null);
  const [columnDialogOpen, setColumnDialogOpen] = useState(false);
  const [columnFormData, setColumnFormData] = useState({
    id: '',
    name: '',
    description: '',
    type: DataType.NUMERIC,
    role: VariableRole.NONE,
    categoryOrder: [] as string[]
  });
  const [isEditingColumn, setIsEditingColumn] = useState(false);
  const [isFullView, setIsFullView] = useState<boolean>(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<DataType | 'all'>('all');
  const [filterRole, setFilterRole] = useState<VariableRole | 'all'>('all');
  const [viewMode, setViewMode] = useState<'table' | 'cards'>('table');
  const [selectedVariables, setSelectedVariables] = useState<Set<string>>(new Set());
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);
  const [bulkMenuAnchor, setBulkMenuAnchor] = useState<null | HTMLElement>(null);
  const [showHidden, setShowHidden] = useState(false);
  const [hiddenVariables, setHiddenVariables] = useState<Set<string>>(new Set());

  const targetDataset = datasets.find(ds => ds.id === selectedDatasetId);

  // Filter variables based on search and filters
  const filteredVariables = useMemo(() => {
    if (!targetDataset) return [];
    
    return targetDataset.columns.filter(column => {
      // Search filter
      if (searchTerm && !column.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
          !(column.description && column.description.toLowerCase().includes(searchTerm.toLowerCase()))) {
        return false;
      }
      
      // Type filter
      if (filterType !== 'all' && column.type !== filterType) {
        return false;
      }
      
      // Role filter
      if (filterRole !== 'all' && column.role !== filterRole) {
        return false;
      }
      
      // Hidden filter
      if (!showHidden && hiddenVariables.has(column.id)) {
        return false;
      }
      
      return true;
    });
  }, [targetDataset, searchTerm, filterType, filterRole, showHidden, hiddenVariables]);

  const toggleFullView = () => {
    setIsFullView(!isFullView);
  };

  const handleDatasetChange = (datasetId: string | null) => {
    setSelectedDatasetId(datasetId);
    const selected = datasets.find(d => d.id === datasetId);
    setCurrentDataset(selected || null);
    setColumnFormData({ id: '', name: '', description: '', type: DataType.NUMERIC, role: VariableRole.NONE, categoryOrder: [] });
    setIsEditingColumn(false);
    setColumnDialogOpen(false);
    setSearchTerm('');
    setFilterType('all');
    setFilterRole('all');
    setSelectedVariables(new Set());
  };
  
  const handleAddColumn = () => {
    setColumnFormData({
      id: '',
      name: '',
      description: '',
      type: DataType.NUMERIC,
      role: VariableRole.NONE,
      categoryOrder: []
    });
    setIsEditingColumn(false);
    setColumnDialogOpen(true);
  };
  
  const handleEditColumn = (columnId: string) => {
    if (targetDataset) {
      const column = targetDataset.columns.find(col => col.id === columnId);
      if (column) {
        setColumnFormData({
          id: column.id,
          name: column.name,
          description: column.description || '',
          type: column.type,
          role: column.role,
          categoryOrder: column.categoryOrder || []
        });
        setIsEditingColumn(true);
        setColumnDialogOpen(true);
      }
    }
  };
  
  // Category ordering functions
  const getUniqueCategories = (columnName: string): string[] => {
    if (!targetDataset) return [];
    const values = targetDataset.data
      .map(row => row[columnName])
      .filter(val => val !== null && val !== undefined && val !== '')
      .map(val => String(val));
    return [...new Set(values)].sort();
  };

  const handleAddCategory = () => {
    const newCategory = `Category ${columnFormData.categoryOrder.length + 1}`;
    setColumnFormData(prev => ({
      ...prev,
      categoryOrder: [...prev.categoryOrder, newCategory]
    }));
  };

  const handleRemoveCategory = (index: number) => {
    setColumnFormData(prev => ({
      ...prev,
      categoryOrder: prev.categoryOrder.filter((_, i) => i !== index)
    }));
  };

  const handleMoveCategoryUp = (index: number) => {
    if (index === 0) return;
    setColumnFormData(prev => {
      const newOrder = [...prev.categoryOrder];
      [newOrder[index - 1], newOrder[index]] = [newOrder[index], newOrder[index - 1]];
      return { ...prev, categoryOrder: newOrder };
    });
  };

  const handleMoveCategoryDown = (index: number) => {
    if (index === columnFormData.categoryOrder.length - 1) return;
    setColumnFormData(prev => {
      const newOrder = [...prev.categoryOrder];
      [newOrder[index], newOrder[index + 1]] = [newOrder[index + 1], newOrder[index]];
      return { ...prev, categoryOrder: newOrder };
    });
  };

  const handleCategoryNameChange = (index: number, newName: string) => {
    setColumnFormData(prev => {
      const newOrder = [...prev.categoryOrder];
      newOrder[index] = newName;
      return { ...prev, categoryOrder: newOrder };
    });
  };

  const handleDetectCategories = () => {
    if (!targetDataset || !columnFormData.name) return;
    const uniqueCategories = getUniqueCategories(columnFormData.name);
    setColumnFormData(prev => ({
      ...prev,
      categoryOrder: uniqueCategories
    }));
  };

  const handleSaveColumn = () => {
    if (!targetDataset || !columnFormData.name.trim()) {
      alert("Variable name cannot be empty.");
      return;
    }
    
    if (isEditingColumn) {
      updateColumn(targetDataset.id, {
        id: columnFormData.id,
        name: columnFormData.name.trim(),
        description: columnFormData.description.trim() || undefined,
        type: columnFormData.type,
        role: columnFormData.role,
        categoryOrder: (columnFormData.type === DataType.CATEGORICAL || columnFormData.type === DataType.ORDINAL)
          ? columnFormData.categoryOrder
          : undefined
      });
    } else {
      if (targetDataset.columns.some(col => col.name.toLowerCase() === columnFormData.name.trim().toLowerCase())) {
        alert(`A variable named "${columnFormData.name.trim()}" already exists in this dataset.`);
        return;
      }
      addColumn(targetDataset.id, {
        name: columnFormData.name.trim(),
        description: columnFormData.description.trim() || undefined,
        type: columnFormData.type,
        role: columnFormData.role,
        categoryOrder: (columnFormData.type === DataType.CATEGORICAL || columnFormData.type === DataType.ORDINAL)
          ? columnFormData.categoryOrder
          : undefined
      });
    }
    
    setColumnDialogOpen(false);
  };
  
  const handleDeleteColumn = (columnId: string) => {
    if (targetDataset) {
      const column = targetDataset.columns.find(col => col.id === columnId);
      if (window.confirm(`Are you sure you want to delete the variable "${column?.name}"? This will remove all data in this column.`)) {
        removeColumn(targetDataset.id, columnId);
      }
    }
  };

  const handleDeleteSelected = () => {
    if (targetDataset && selectedVariables.size > 0) {
      if (window.confirm(`Delete ${selectedVariables.size} selected variable(s)? This will remove all associated data.`)) {
        selectedVariables.forEach(varId => {
          removeColumn(targetDataset.id, varId);
        });
        setSelectedVariables(new Set());
      }
    }
  };

  const toggleVariableSelection = (varId: string) => {
    const newSelection = new Set(selectedVariables);
    if (newSelection.has(varId)) {
      newSelection.delete(varId);
    } else {
      newSelection.add(varId);
    }
    setSelectedVariables(newSelection);
  };

  const selectAllVariables = () => {
    if (selectedVariables.size === filteredVariables.length) {
      setSelectedVariables(new Set());
    } else {
      setSelectedVariables(new Set(filteredVariables.map(v => v.id)));
    }
  };

  const getDataTypeIcon = (type: DataType) => {
    switch (type) {
      case DataType.NUMERIC:
        return <NumbersIcon fontSize="small" />;
      case DataType.TEXT:
        return <TextFieldsIcon fontSize="small" />;
      case DataType.BOOLEAN:
        return <ToggleOnIcon fontSize="small" />;
      case DataType.DATE:
        return <CalendarIcon fontSize="small" />;
      case DataType.CATEGORICAL:
        return <CategoryIcon fontSize="small" />;
      case DataType.ORDINAL:
        return <SignalCellularAltIcon fontSize="small" />; // Using SignalCellularAltIcon for Ordinal
      default:
        return <DatasetIcon fontSize="small" />;
    }
  };

  const getDataTypeColor = (type: DataType) => {
    switch (type) {
      case DataType.NUMERIC:
        return theme.palette.info.main;
      case DataType.TEXT:
        return theme.palette.warning.main;
      case DataType.BOOLEAN:
        return theme.palette.success.main;
      case DataType.DATE:
        return theme.palette.error.main;
      case DataType.CATEGORICAL:
      case DataType.ORDINAL:
        return theme.palette.secondary.main;
      default:
        return theme.palette.text.secondary;
    }
  };

  const getRoleIcon = (role: VariableRole) => {
    switch (role) {
      case VariableRole.INDEPENDENT:
        return <FunctionsIcon fontSize="small" />;
      case VariableRole.DEPENDENT:
        return <TrendingUpIcon fontSize="small" />;
      case VariableRole.COVARIATE:
        return <PsychologyIcon fontSize="small" />;
      default:
        return null;
    }
  };

  const getRoleColor = (role: VariableRole) => {
    switch (role) {
      case VariableRole.INDEPENDENT:
        return theme.palette.primary.main;
      case VariableRole.DEPENDENT:
        return theme.palette.secondary.main;
      case VariableRole.COVARIATE:
        return theme.palette.warning.main;
      default:
        return theme.palette.text.secondary;
    }
  };

  const getDataTypeLabel = (type: DataType) => {
    switch (type) {
      case DataType.NUMERIC: return 'Numeric';
      case DataType.CATEGORICAL: return 'Categorical';
      case DataType.ORDINAL: return 'Ordinal';
      case DataType.DATE: return 'Date';
      case DataType.TEXT: return 'Text';
      case DataType.BOOLEAN: return 'Boolean';
      default: return 'Unknown';
    }
  };
  
  const getRoleLabel = (role: VariableRole) => {
    switch (role) {
      case VariableRole.INDEPENDENT: return 'Independent';
      case VariableRole.DEPENDENT: return 'Dependent';
      case VariableRole.COVARIATE: return 'Covariate';
      case VariableRole.NONE: return 'None';
      default: return 'Unknown';
    }
  };

  const renderVariableCard = (column: any) => {
    const isSelected = selectedVariables.has(column.id);
    const isHidden = hiddenVariables.has(column.id);
    
    return (
      <Grid item xs={12} sm={6} md={4} key={column.id}>
        <Card
          variant="outlined"
          sx={{
            position: 'relative',
            borderColor: isSelected ? 'primary.main' : 'divider',
            borderWidth: isSelected ? 2 : 1,
            opacity: isHidden ? 0.6 : 1,
            transition: 'all 0.2s ease',
            '&:hover': {
              boxShadow: theme.shadows[4],
              transform: 'translateY(-2px)'
            }
          }}
        >
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Checkbox
                  checked={isSelected}
                  onChange={() => toggleVariableSelection(column.id)}
                  size="small"
                />
                <Avatar 
                  sx={{ 
                    bgcolor: alpha(getDataTypeColor(column.type), 0.1),
                    color: getDataTypeColor(column.type),
                    width: 32,
                    height: 32
                  }}
                >
                  {getDataTypeIcon(column.type)}
                </Avatar>
              </Box>
              <IconButton
                size="small"
                onClick={(e) => {
                  setMenuAnchor(e.currentTarget);
                  setSelectedVariables(new Set([column.id]));
                }}
              >
                <MoreVertIcon />
              </IconButton>
            </Box>
            
            <Typography variant="h6" gutterBottom noWrap>
              {column.name}
            </Typography>
            
            <Stack direction="row" spacing={1} mb={1}>
              <Chip
                label={getDataTypeLabel(column.type)}
                size="small"
                sx={{
                  bgcolor: alpha(getDataTypeColor(column.type), 0.1),
                  color: getDataTypeColor(column.type),
                  fontWeight: 500
                }}
              />
              {column.role !== VariableRole.NONE && (
                <Chip
                  icon={getRoleIcon(column.role) || undefined}
                  label={getRoleLabel(column.role)}
                  size="small"
                  sx={{
                    bgcolor: alpha(getRoleColor(column.role), 0.1),
                    color: getRoleColor(column.role),
                    fontWeight: 500
                  }}
                />
              )}
              {isHidden && (
                <Chip
                  icon={<VisibilityOffIcon />}
                  label="Hidden"
                  size="small"
                  color="default"
                />
              )}
            </Stack>
            
            {column.description && (
              <Typography variant="body2" color="text.secondary" sx={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical',
              }}>
                {column.description}
              </Typography>
            )}
          </CardContent>
        </Card>
      </Grid>
    );
  };

  return (
    <Container maxWidth={isFullView ? false : 'xl'} sx={{ py: isFullView ? 0 : 2 }}>
      {!isFullView && (
        <>
          {/* Compact Dataset Selection */}
          <Card sx={{ mb: 2, borderRadius: 2 }}>
            <CardContent sx={{ py: 1.5, '&:last-child': { pb: 1.5 } }}>
              <Stack direction="row" spacing={1.5} alignItems="center">
                <Avatar sx={{
                  bgcolor: theme.palette.primary.main,
                  width: 32,
                  height: 32
                }}>
                  <DatasetIcon fontSize="small" />
                </Avatar>
                <Box sx={{ flex: 1, minWidth: 0 }}>
                  <Typography variant="subtitle2" fontWeight="medium" sx={{ mb: 0.5 }}>
                    Dataset Variables
                  </Typography>
                  <DatasetSelector
                    value={selectedDatasetId || ''}
                    onChange={handleDatasetChange}
                    label="Select Dataset to Edit Variables"
                  />
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </>
      )}

      {targetDataset ? (
        <Card elevation={isFullView ? 0 : 2} sx={{ borderRadius: isFullView ? 0 : 2, overflow: 'hidden' }}>
          {/* Optimized Toolbar */}
          <Box
            sx={{
              px: 2,
              py: 1.5,
              bgcolor: 'background.paper',
              borderBottom: 1,
              borderColor: 'divider',
              boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
            }}
          >
            {/* Single Row Layout for Better Space Utilization */}
            <Stack
              direction={{ xs: 'column', lg: 'row' }}
              spacing={{ xs: 1.5, lg: 2 }}
              alignItems={{ lg: 'center' }}
              justifyContent="space-between"
            >
              {/* Left Section: Dataset Info and Search */}
              <Stack direction={{ xs: 'column', md: 'row' }} spacing={1.5} alignItems={{ md: 'center' }} sx={{ minWidth: 0, flex: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Typography variant="h6" noWrap>
                    {targetDataset.name}
                  </Typography>
                  <Stack direction="row" spacing={0.5}>
                    <Chip
                      icon={<DatasetIcon />}
                      label={`${targetDataset.columns.length} total`}
                      size="small"
                      variant="outlined"
                    />
                    {searchTerm || filterType !== 'all' || filterRole !== 'all' ? (
                      <Chip
                        icon={<FilterListIcon />}
                        label={`${filteredVariables.length} shown`}
                        size="small"
                        color="primary"
                        variant="outlined"
                      />
                    ) : null}
                    {selectedVariables.size > 0 && (
                      <Chip
                        label={`${selectedVariables.size} selected`}
                        size="small"
                        color="secondary"
                        onDelete={() => setSelectedVariables(new Set())}
                      />
                    )}
                  </Stack>
                </Box>
              </Stack>

              {/* Right Section: Search, Filters and Actions */}
              <Stack direction="row" spacing={1} alignItems="center" sx={{ flexShrink: 0 }}>
                {/* Search */}
                <TextField
                  placeholder="Search variables..."
                  size="small"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon fontSize="small" />
                      </InputAdornment>
                    ),
                    endAdornment: searchTerm && (
                      <InputAdornment position="end">
                        <IconButton size="small" onClick={() => setSearchTerm('')}>
                          <ClearIcon fontSize="small" />
                        </IconButton>
                      </InputAdornment>
                    )
                  }}
                  sx={{
                    width: { xs: '100%', md: 200 },
                    '& .MuiOutlinedInput-root': {
                      backgroundColor: 'background.default'
                    }
                  }}
                />

                {/* Compact Filters */}
                <FormControl size="small" sx={{ minWidth: 100 }}>
                  <InputLabel>Type</InputLabel>
                  <Select
                    value={filterType}
                    label="Type"
                    onChange={(e) => setFilterType(e.target.value as DataType | 'all')}
                  >
                    <MenuItem value="all">All</MenuItem>
                    <Divider />
                    <MenuItem value={DataType.NUMERIC}>
                      <Stack direction="row" spacing={1} alignItems="center">
                        <NumbersIcon fontSize="small" />
                        <span>Numeric</span>
                      </Stack>
                    </MenuItem>
                    <MenuItem value={DataType.TEXT}>
                      <Stack direction="row" spacing={1} alignItems="center">
                        <TextFieldsIcon fontSize="small" />
                        <span>Text</span>
                      </Stack>
                    </MenuItem>
                    <MenuItem value={DataType.CATEGORICAL}>
                      <Stack direction="row" spacing={1} alignItems="center">
                        <CategoryIcon fontSize="small" />
                        <span>Categorical</span>
                      </Stack>
                    </MenuItem>
                    <MenuItem value={DataType.BOOLEAN}>
                      <Stack direction="row" spacing={1} alignItems="center">
                        <ToggleOnIcon fontSize="small" />
                        <span>Boolean</span>
                      </Stack>
                    </MenuItem>
                    <MenuItem value={DataType.DATE}>
                      <Stack direction="row" spacing={1} alignItems="center">
                        <CalendarIcon fontSize="small" />
                        <span>Date</span>
                      </Stack>
                    </MenuItem>
                  </Select>
                </FormControl>

                <FormControl size="small" sx={{ minWidth: 100 }}>
                  <InputLabel>Role</InputLabel>
                  <Select
                    value={filterRole}
                    label="Role"
                    onChange={(e) => setFilterRole(e.target.value as VariableRole | 'all')}
                  >
                    <MenuItem value="all">All</MenuItem>
                    <Divider />
                    <MenuItem value={VariableRole.NONE}>None</MenuItem>
                    <MenuItem value={VariableRole.INDEPENDENT}>Independent</MenuItem>
                    <MenuItem value={VariableRole.DEPENDENT}>Dependent</MenuItem>
                    <MenuItem value={VariableRole.COVARIATE}>Covariate</MenuItem>
                  </Select>
                </FormControl>

                {/* Action Buttons */}
                <Stack direction="row" spacing={0.5} alignItems="center">
                  <Button
                    variant="contained"
                    size="small"
                    startIcon={<AddIcon />}
                    onClick={handleAddColumn}
                    sx={{ px: 1.5 }}
                  >
                    Add
                  </Button>

                  {selectedVariables.size > 0 && (
                    <Tooltip title={`Delete ${selectedVariables.size} selected variable${selectedVariables.size > 1 ? 's' : ''}`}>
                      <IconButton
                        size="small"
                        color="error"
                        onClick={handleDeleteSelected}
                        sx={{
                          border: 1,
                          borderColor: 'error.main',
                          '&:hover': { borderColor: 'error.dark' }
                        }}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  )}

                  <ToggleButtonGroup
                    value={viewMode}
                    exclusive
                    onChange={(_, newMode) => newMode && setViewMode(newMode)}
                    size="small"
                    sx={{ display: { xs: 'none', sm: 'flex' } }}
                  >
                    <ToggleButton value="table" sx={{ px: 1 }}>
                      <Tooltip title="Table view">
                        <ViewListIcon fontSize="small" />
                      </Tooltip>
                    </ToggleButton>
                    <ToggleButton value="cards" sx={{ px: 1 }}>
                      <Tooltip title="Card view">
                        <ViewModuleIcon fontSize="small" />
                      </Tooltip>
                    </ToggleButton>
                  </ToggleButtonGroup>

                  {onGoToDataEditor && (
                    <Tooltip title="View data">
                      <IconButton
                        size="small"
                        onClick={() => onGoToDataEditor(targetDataset.id)}
                        sx={{
                          border: 1,
                          borderColor: 'divider',
                          '&:hover': { borderColor: 'primary.main' }
                        }}
                      >
                        <TableViewIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  )}

                  <Tooltip title={isFullView ? "Exit fullscreen" : "Fullscreen"}>
                    <IconButton
                      size="small"
                      onClick={toggleFullView}
                      sx={{
                        border: 1,
                        borderColor: 'divider',
                        '&:hover': { borderColor: 'primary.main' }
                      }}
                    >
                      {isFullView ? <FullscreenExitIcon fontSize="small" /> : <FullscreenIcon fontSize="small" />}
                    </IconButton>
                  </Tooltip>
                </Stack>
              </Stack>
            </Stack>
          </Box>

          {/* Optimized Content Area */}
          <Box sx={{ p: 1.5 }}>
            {filteredVariables.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <DatasetIcon sx={{ fontSize: 64, color: 'text.disabled', mb: 2 }} />
                <Typography variant="h6" gutterBottom color="text.secondary">
                  No Variables Found
                </Typography>
                <Typography variant="body2" color="text.secondary" paragraph>
                  {searchTerm || filterType !== 'all' || filterRole !== 'all'
                    ? 'Try adjusting your search or filters'
                    : 'Add variables to define your dataset structure'}
                </Typography>
                {targetDataset.columns.length === 0 && (
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={handleAddColumn}
                    sx={{ mt: 2 }}
                  >
                    Add First Variable
                  </Button>
                )}
              </Box>
            ) : viewMode === 'table' ? (
              <TableContainer sx={{
                maxHeight: isFullView ? 'calc(100vh - 200px)' : 600,
                '& .MuiTableHead-root': {
                  '& .MuiTableRow-root': {
                    '& .MuiTableCell-root': {
                      position: 'sticky',
                      top: 0,
                      zIndex: 100,
                      backgroundColor: theme.palette.mode === 'dark'
                        ? theme.palette.grey[800]
                        : theme.palette.grey[100],
                      borderBottom: `2px solid ${theme.palette.divider}`,
                      fontWeight: 600,
                      color: theme.palette.text.primary,
                      py: 1
                    }
                  }
                }
              }}>
                <Table stickyHeader size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell padding="checkbox" sx={{ width: 48 }}>
                        <Checkbox
                          size="small"
                          indeterminate={selectedVariables.size > 0 && selectedVariables.size < filteredVariables.length}
                          checked={filteredVariables.length > 0 && selectedVariables.size === filteredVariables.length}
                          onChange={selectAllVariables}
                        />
                      </TableCell>
                      <TableCell sx={{ minWidth: 120 }}>Name</TableCell>
                      <TableCell sx={{ width: 100 }}>Type</TableCell>
                      <TableCell sx={{ width: 120 }}>Role</TableCell>
                      <TableCell>Description</TableCell>
                      <TableCell align="right" sx={{ width: 80 }}>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {filteredVariables.map(column => {
                      const isSelected = selectedVariables.has(column.id);
                      const isHidden = hiddenVariables.has(column.id);
                      
                      return (
                        <TableRow 
                          key={column.id}
                          hover
                          selected={isSelected}
                          sx={{ opacity: isHidden ? 0.6 : 1 }}
                        >
                          <TableCell padding="checkbox">
                            <Checkbox
                              size="small"
                              checked={isSelected}
                              onChange={() => toggleVariableSelection(column.id)}
                            />
                          </TableCell>
                          <TableCell sx={{ py: 1 }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Avatar
                                sx={{
                                  bgcolor: alpha(getDataTypeColor(column.type), 0.1),
                                  color: getDataTypeColor(column.type),
                                  width: 24,
                                  height: 24
                                }}
                              >
                                {getDataTypeIcon(column.type)}
                              </Avatar>
                              <Typography variant="body2" fontWeight={500} noWrap>
                                {column.name}
                              </Typography>
                              {isHidden && (
                                <Tooltip title="Hidden variable">
                                  <VisibilityOffIcon fontSize="small" color="disabled" />
                                </Tooltip>
                              )}
                            </Box>
                          </TableCell>
                          <TableCell sx={{ py: 1 }}>
                            <Chip
                              label={getDataTypeLabel(column.type)}
                              size="small"
                              variant="outlined"
                              sx={{
                                bgcolor: alpha(getDataTypeColor(column.type), 0.05),
                                color: getDataTypeColor(column.type),
                                borderColor: alpha(getDataTypeColor(column.type), 0.3),
                                fontWeight: 500,
                                height: 24
                              }}
                            />
                          </TableCell>
                          <TableCell sx={{ py: 1 }}>
                            {column.role !== VariableRole.NONE ? (
                              <Chip
                                icon={getRoleIcon(column.role) || undefined}
                                label={getRoleLabel(column.role)}
                                size="small"
                                variant="outlined"
                                sx={{
                                  bgcolor: alpha(getRoleColor(column.role), 0.05),
                                  color: getRoleColor(column.role),
                                  borderColor: alpha(getRoleColor(column.role), 0.3),
                                  fontWeight: 500,
                                  height: 24
                                }}
                              />
                            ) : (
                              <Typography variant="body2" color="text.secondary">-</Typography>
                            )}
                          </TableCell>
                          <TableCell sx={{ py: 1 }}>
                            <Typography variant="body2" color="text.secondary" noWrap>
                              {column.description || '-'}
                            </Typography>
                          </TableCell>
                          <TableCell align="right">
                            <Stack direction="row" spacing={0.5} justifyContent="flex-end">
                              <Tooltip title="Edit variable">
                                <IconButton 
                                  size="small" 
                                  onClick={() => handleEditColumn(column.id)}
                                >
                                  <EditIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Delete variable">
                                <IconButton 
                                  size="small" 
                                  onClick={() => handleDeleteColumn(column.id)}
                                  color="error"
                                >
                                  <DeleteIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            </Stack>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <Grid container spacing={2}>
                {filteredVariables.map(column => renderVariableCard(column))}
              </Grid>
            )}
          </Box>
        </Card>
      ) : (
        <Card sx={{ textAlign: 'center', py: 8 }}>
          <DatasetIcon sx={{ fontSize: 80, color: 'text.disabled', mb: 2 }} />
          <Typography variant="h5" gutterBottom color="text.secondary">
            No Dataset Selected
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            Select a dataset above to view and manage its variables
          </Typography>
        </Card>
      )}

      {/* Variable Dialog */}
      <Dialog 
        open={columnDialogOpen} 
        onClose={() => setColumnDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          {isEditingColumn ? 'Edit Variable' : 'Add New Variable'}
        </DialogTitle>
        <DialogContent dividers>
          <Stack spacing={3} sx={{ mt: 1 }}>
            <TextField
              label="Variable Name"
              value={columnFormData.name}
              onChange={(e) => setColumnFormData({ ...columnFormData, name: e.target.value })}
              fullWidth
              required
              autoFocus
              helperText="Choose a descriptive name for your variable"
            />
            
            <TextField
              label="Description"
              value={columnFormData.description}
              onChange={(e) => setColumnFormData({ ...columnFormData, description: e.target.value })}
              fullWidth
              multiline
              rows={3}
              helperText="Optional: Describe what this variable represents"
            />
            
            <FormControl fullWidth>
              <InputLabel>Data Type</InputLabel>
              <Select
                value={columnFormData.type}
                label="Data Type"
                onChange={(e) => setColumnFormData({ ...columnFormData, type: e.target.value as DataType })}
              >
                <MenuItem value={DataType.NUMERIC}>
                  <Stack direction="row" spacing={1} alignItems="center">
                    <NumbersIcon fontSize="small" />
                    <Box>
                      <Typography variant="body1">Numeric</Typography>
                      <Typography variant="caption" color="text.secondary">
                        Numbers (integers or decimals)
                      </Typography>
                    </Box>
                  </Stack>
                </MenuItem>
                <MenuItem value={DataType.TEXT}>
                  <Stack direction="row" spacing={1} alignItems="center">
                    <TextFieldsIcon fontSize="small" />
                    <Box>
                      <Typography variant="body1">Text</Typography>
                      <Typography variant="caption" color="text.secondary">
                        Free-form text values
                      </Typography>
                    </Box>
                  </Stack>
                </MenuItem>
                <MenuItem value={DataType.CATEGORICAL}>
                  <Stack direction="row" spacing={1} alignItems="center">
                    <CategoryIcon fontSize="small" />
                    <Box>
                      <Typography variant="body1">Categorical</Typography>
                      <Typography variant="caption" color="text.secondary">
                        Distinct categories without order
                      </Typography>
                    </Box>
                  </Stack>
                </MenuItem>
                <MenuItem value={DataType.ORDINAL}>
                  <Stack direction="row" spacing={1} alignItems="center">
                    <SignalCellularAltIcon fontSize="small" />
                    <Box>
                      <Typography variant="body1">Ordinal</Typography>
                      <Typography variant="caption" color="text.secondary">
                        Ordered categories (e.g., Low, Medium, High)
                      </Typography>
                    </Box>
                  </Stack>
                </MenuItem>
                <MenuItem value={DataType.BOOLEAN}>
                  <Stack direction="row" spacing={1} alignItems="center">
                    <ToggleOnIcon fontSize="small" />
                    <Box>
                      <Typography variant="body1">Boolean</Typography>
                      <Typography variant="caption" color="text.secondary">
                        True/False values
                      </Typography>
                    </Box>
                  </Stack>
                </MenuItem>
                <MenuItem value={DataType.DATE}>
                  <Stack direction="row" spacing={1} alignItems="center">
                    <CalendarIcon fontSize="small" />
                    <Box>
                      <Typography variant="body1">Date</Typography>
                      <Typography variant="caption" color="text.secondary">
                        Date and time values
                      </Typography>
                    </Box>
                  </Stack>
                </MenuItem>
              </Select>
              <FormHelperText>Select the type of data this variable will contain</FormHelperText>
            </FormControl>
            
            <FormControl fullWidth>
              <InputLabel>Variable Role</InputLabel>
              <Select
                value={columnFormData.role}
                label="Variable Role"
                onChange={(e) => setColumnFormData({ ...columnFormData, role: e.target.value as VariableRole })}
              >
                <MenuItem value={VariableRole.NONE}>
                  <Box>
                    <Typography variant="body1">None</Typography>
                    <Typography variant="caption" color="text.secondary">
                      No specific role in analysis
                    </Typography>
                  </Box>
                </MenuItem>
                <MenuItem value={VariableRole.INDEPENDENT}>
                  <Stack direction="row" spacing={1} alignItems="center">
                    <FunctionsIcon fontSize="small" color="primary" />
                    <Box>
                      <Typography variant="body1">Independent Variable</Typography>
                      <Typography variant="caption" color="text.secondary">
                        Predictor or input variable
                      </Typography>
                    </Box>
                  </Stack>
                </MenuItem>
                <MenuItem value={VariableRole.DEPENDENT}>
                  <Stack direction="row" spacing={1} alignItems="center">
                    <TrendingUpIcon fontSize="small" color="secondary" />
                    <Box>
                      <Typography variant="body1">Dependent Variable</Typography>
                      <Typography variant="caption" color="text.secondary">
                        Outcome or target variable
                      </Typography>
                    </Box>
                  </Stack>
                </MenuItem>
                <MenuItem value={VariableRole.COVARIATE}>
                  <Stack direction="row" spacing={1} alignItems="center">
                    <PsychologyIcon fontSize="small" color="warning" />
                    <Box>
                      <Typography variant="body1">Covariate</Typography>
                      <Typography variant="caption" color="text.secondary">
                        Control variable in analysis
                      </Typography>
                    </Box>
                  </Stack>
                </MenuItem>
              </Select>
              <FormHelperText>Define the role of this variable in your analysis</FormHelperText>
            </FormControl>

            {/* Category Ordering Section */}
            {(columnFormData.type === DataType.CATEGORICAL || columnFormData.type === DataType.ORDINAL) && (
              <Box sx={{ mt: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="subtitle1" fontWeight="medium">
                    Category Order
                  </Typography>
                  <Stack direction="row" spacing={1}>
                    {isEditingColumn && (
                      <Button
                        size="small"
                        variant="outlined"
                        onClick={handleDetectCategories}
                        startIcon={<SearchIcon />}
                      >
                        Detect from Data
                      </Button>
                    )}
                    <Button
                      size="small"
                      variant="outlined"
                      onClick={handleAddCategory}
                      startIcon={<AddCategoryIcon />}
                    >
                      Add Category
                    </Button>
                  </Stack>
                </Box>

                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {columnFormData.type === DataType.ORDINAL
                    ? "Define the logical order of categories (e.g., Low → Medium → High)"
                    : "Optionally define a custom display order for categories"
                  }
                </Typography>

                {columnFormData.categoryOrder.length === 0 ? (
                  <Box sx={{
                    p: 3,
                    border: 1,
                    borderColor: 'divider',
                    borderRadius: 1,
                    textAlign: 'center',
                    bgcolor: 'background.default'
                  }}>
                    <Typography variant="body2" color="text.secondary">
                      No categories defined. Add categories manually or detect them from existing data.
                    </Typography>
                  </Box>
                ) : (
                  <Box sx={{
                    border: 1,
                    borderColor: 'divider',
                    borderRadius: 1,
                    bgcolor: 'background.default',
                    maxHeight: 300,
                    overflow: 'auto'
                  }}>
                    {columnFormData.categoryOrder.map((category, index) => (
                      <Box
                        key={index}
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          p: 1.5,
                          borderBottom: index < columnFormData.categoryOrder.length - 1 ? 1 : 0,
                          borderColor: 'divider',
                          '&:hover': { bgcolor: 'action.hover' }
                        }}
                      >
                        <DragIndicatorIcon sx={{ color: 'text.disabled', mr: 1 }} />

                        <Typography variant="body2" sx={{
                          minWidth: 24,
                          textAlign: 'center',
                          color: 'text.secondary',
                          mr: 1
                        }}>
                          {index + 1}.
                        </Typography>

                        <TextField
                          size="small"
                          value={category}
                          onChange={(e) => handleCategoryNameChange(index, e.target.value)}
                          sx={{ flex: 1, mr: 1 }}
                          variant="outlined"
                        />

                        <Stack direction="row" spacing={0.5}>
                          <IconButton
                            size="small"
                            onClick={() => handleMoveCategoryUp(index)}
                            disabled={index === 0}
                          >
                            <ArrowUpIcon fontSize="small" />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() => handleMoveCategoryDown(index)}
                            disabled={index === columnFormData.categoryOrder.length - 1}
                          >
                            <ArrowDownIcon fontSize="small" />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() => handleRemoveCategory(index)}
                            color="error"
                          >
                            <DeleteCategoryIcon fontSize="small" />
                          </IconButton>
                        </Stack>
                      </Box>
                    ))}
                  </Box>
                )}
              </Box>
            )}
          </Stack>
        </DialogContent>
        <DialogActions sx={{ p: 3 }}>
          <Button onClick={() => setColumnDialogOpen(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleSaveColumn} 
            variant="contained" 
            disabled={!columnFormData.name.trim()}
          >
            {isEditingColumn ? 'Save Changes' : 'Add Variable'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Context Menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={() => setMenuAnchor(null)}
      >
        <MenuItem onClick={() => {
          if (selectedVariables.size === 1) {
            handleEditColumn(Array.from(selectedVariables)[0]);
          }
          setMenuAnchor(null);
        }}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Edit</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => {
          // Duplicate logic would go here
          setMenuAnchor(null);
        }}>
          <ListItemIcon>
            <ContentCopyIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Duplicate</ListItemText>
        </MenuItem>
        <Divider />
        <MenuItem onClick={() => {
          selectedVariables.forEach(id => {
            handleDeleteColumn(id);
          });
          setMenuAnchor(null);
        }}>
          <ListItemIcon>
            <DeleteIcon fontSize="small" color="error" />
          </ListItemIcon>
          <ListItemText>Delete</ListItemText>
        </MenuItem>
      </Menu>
    </Container>
  );
};

export default VariableEditor;
