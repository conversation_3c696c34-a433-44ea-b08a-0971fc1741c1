import { Dataset, Column, DataType, VariableRole, DataRow } from '../types';
import {
  calculateMean,
  calculateStandardDeviation,
  calculateMedian,
  isNormallyDistributed,
  calculateSkewness,
  calculateKurtosis
} from './stats';
import {
  checkMissingValues,
  checkOutliers,
  calculateFrequencies,
  detectDataType
} from './dataUtilities';

// Enhanced data quality assessment interfaces
export interface MissingDataPattern {
  type: 'MCAR' | 'MAR' | 'MNAR' | 'UNKNOWN';
  confidence: number;
  description: string;
  recommendation: string;
}

export interface OutlierDetectionResult {
  method: 'IQR' | 'Z_SCORE' | 'MODIFIED_Z_SCORE' | 'MAHALANOBIS';
  outliers: Array<{
    rowIndex: number;
    value: any;
    severity: 'mild' | 'moderate' | 'extreme';
    zScore?: number;
    mahalanobisDistance?: number;
  }>;
  threshold: number;
  recommendation: string;
}

export interface DistributionAssessment {
  normality: {
    isNormal: boolean;
    pValue: number;
    statistic: number;
    skewness: number;
    kurtosis: number;
    interpretation: string;
  };
  balance?: {
    isBalanced: boolean;
    imbalanceRatio: number;
    minorityClassPercentage: number;
    majorityClassPercentage: number;
    recommendation: string;
    categoryDetails: {
      mostFrequent: { category: string; count: number; percentage: number };
      leastFrequent: { category: string; count: number; percentage: number };
      allCategories: Array<{ category: string; count: number; percentage: number }>;
    };
  };
  transformationSuggestions: string[];
}

export interface ConsistencyCheck {
  type: 'LOGICAL' | 'RANGE' | 'FORMAT' | 'RELATIONSHIP';
  variable: string;
  issues: Array<{
    rowIndex: number;
    value: any;
    description: string;
    severity: 'low' | 'medium' | 'high';
  }>;
  recommendation: string;
}

export interface SampleSizeAssessment {
  isAdequate: boolean;
  currentSize: number;
  recommendedMinimum: number;
  powerAnalysis?: {
    power: number;
    effectSize: number;
    alpha: number;
  };
  recommendations: string[];
}

export interface DataQualityAssessment {
  overallScore: number; // 0-100
  overallGrade: 'excellent' | 'good' | 'fair' | 'poor';
  identifierVariables: Array<{
    variable: string;
    analysis: {
      uniquenessPercentage: number;
      hasSequentialPattern: boolean;
      hasAlphanumericPattern: boolean;
      completenessPercentage: number;
      duplicateCount: number;
      recommendations: string[];
    };
  }>;
  missingDataAnalysis: {
    totalMissing: number;
    missingPercentage: number;
    patternsByVariable: Record<string, MissingDataPattern>;
    overallPattern: MissingDataPattern;
    excludedIdentifiers: string[];
  };
  outlierAnalysis: Record<string, OutlierDetectionResult[]>;
  distributionAnalysis: Record<string, DistributionAssessment>;
  consistencyChecks: ConsistencyCheck[];
  dataTypeValidation: Array<{
    variable: string;
    currentType: DataType;
    suggestedType: DataType;
    confidence: number;
    reason: string;
  }>;
  sampleSizeAssessment: SampleSizeAssessment;
  prioritizedRecommendations: Array<{
    priority: 'critical' | 'high' | 'medium' | 'low';
    category: 'missing_data' | 'outliers' | 'distribution' | 'consistency' | 'sample_size' | 'data_types' | 'identifiers';
    title: string;
    description: string;
    actionSteps: string[];
    impact: string;
  }>;
}

export interface DatasetAnalysis {
  hasData: boolean;
  totalRows: number;
  totalColumns: number;
  variableTypes: {
    numeric: number;
    categorical: number;
    ordinal: number;
    date: number;
    text: number;
    boolean: number;
  };
  dataQuality: {
    totalMissingValues: number;
    missingValuesByColumn: Record<string, number>;
    columnsWithMissingData: string[];
    missingDataPercentage: number;
  };
  variableAnalysis: VariableAnalysis[];
  recommendations: AnalysisRecommendation[];
  // Enhanced data quality assessment
  qualityAssessment?: DataQualityAssessment;
}

export interface VariableAnalysis {
  columnId: string;
  columnName: string;
  type: DataType;
  role: VariableRole;
  uniqueValues: number;
  missingCount: number;
  missingPercentage: number;
  isIdentifier: boolean;
  // Numeric-specific
  mean?: number;
  median?: number;
  standardDeviation?: number;
  isNormal?: boolean;
  hasOutliers?: boolean;
  // Categorical-specific
  categories?: string[];
  categoryCount?: number;
  mostFrequentCategory?: string;
  // General insights
  insights: string[];
  // Identifier-specific properties
  identifierAnalysis?: {
    uniquenessPercentage: number;
    hasSequentialPattern: boolean;
    hasAlphanumericPattern: boolean;
    completenessPercentage: number;
    duplicateCount: number;
    recommendations: string[];
  };
}

export interface AnalysisRecommendation {
  id: string;
  title: string;
  description: string;
  reason: string;
  analysisType: string;
  category: string;
  priority: 'high' | 'medium' | 'low';
  variables: string[];
  path?: string;
  prerequisites?: string[];
}

/**
 * Analyzes a dataset and provides intelligent insights and recommendations
 */
export const analyzeDataset = (dataset: Dataset | null): DatasetAnalysis => {
  if (!dataset || !dataset.data || dataset.data.length === 0) {
    return {
      hasData: false,
      totalRows: 0,
      totalColumns: 0,
      variableTypes: {
        numeric: 0,
        categorical: 0,
        ordinal: 0,
        date: 0,
        text: 0,
        boolean: 0
      },
      dataQuality: {
        totalMissingValues: 0,
        missingValuesByColumn: {},
        columnsWithMissingData: [],
        missingDataPercentage: 0
      },
      variableAnalysis: [],
      recommendations: []
    };
  }

  const totalRows = dataset.data.length;
  const totalColumns = dataset.columns.length;

  // Analyze variable types
  const variableTypes = {
    numeric: 0,
    categorical: 0,
    ordinal: 0,
    date: 0,
    text: 0,
    boolean: 0
  };

  dataset.columns.forEach(column => {
    variableTypes[column.type]++;
  });

  // Analyze data quality
  const missingValueReport = checkMissingValues(dataset.data);
  const dataQuality = {
    totalMissingValues: missingValueReport.total,
    missingValuesByColumn: missingValueReport.byColumn,
    columnsWithMissingData: Object.keys(missingValueReport.byColumn).filter(
      col => missingValueReport.byColumn[col] > 0
    ),
    missingDataPercentage: (missingValueReport.total / (totalRows * totalColumns)) * 100
  };

  // Analyze individual variables
  const variableAnalysis = dataset.columns.map(column => 
    analyzeVariable(column, dataset.data)
  );

  // Generate recommendations
  const recommendations = generateRecommendations(dataset, variableAnalysis, dataQuality);

  // Perform comprehensive data quality assessment
  const qualityAssessment = performComprehensiveDataQualityAssessment(dataset);

  return {
    hasData: true,
    totalRows,
    totalColumns,
    variableTypes,
    dataQuality,
    variableAnalysis,
    recommendations,
    qualityAssessment
  };
};

/**
 * Detects variable naming patterns for scale analysis
 */
const detectScalePatterns = (variables: VariableAnalysis[]): {
  scaleGroups: Array<{
    prefix: string;
    variables: string[];
    count: number;
  }>;
  hasScales: boolean;
} => {
  const patterns: Record<string, string[]> = {};

  variables.forEach(variable => {
    const name = variable.columnName.toLowerCase();

    // Look for patterns like "anxiety_1", "depression_scale_1", "item1", etc.
    const patterns_regex = [
      /^([a-z]+)_\d+$/,           // prefix_number
      /^([a-z]+)_scale_\d+$/,     // prefix_scale_number
      /^([a-z]+)_item_?\d+$/,     // prefix_item_number
      /^([a-z]+)\d+$/,            // prefixnumber
      /^(item|question|q)\d+$/,   // item/question patterns
    ];

    for (const regex of patterns_regex) {
      const match = name.match(regex);
      if (match) {
        const prefix = match[1];
        if (!patterns[prefix]) {
          patterns[prefix] = [];
        }
        patterns[prefix].push(variable.columnName);
        break;
      }
    }
  });

  // Filter groups with at least 3 variables (minimum for factor analysis)
  const scaleGroups = Object.entries(patterns)
    .filter(([_, vars]) => vars.length >= 3)
    .map(([prefix, vars]) => ({
      prefix,
      variables: vars,
      count: vars.length
    }));

  return {
    scaleGroups,
    hasScales: scaleGroups.length > 0
  };
};

/**
 * Detects survival analysis related variables
 */
const detectSurvivalVariables = (variables: VariableAnalysis[]): {
  survivalVariables: string[];
  hasSurvivalData: boolean;
  timeVariable?: string;
  eventVariable?: string;
} => {
  const survivalKeywords = [
    'survival', 'time', 'duration', 'follow', 'followup', 'follow_up',
    'recurrence', 'event', 'status', 'death', 'mortality', 'censored',
    'endpoint', 'outcome', 'relapse', 'progression'
  ];

  const timeKeywords = ['time', 'duration', 'follow', 'followup', 'follow_up', 'survival'];
  const eventKeywords = ['event', 'status', 'death', 'mortality', 'censored', 'outcome'];

  const survivalVariables: string[] = [];
  let timeVariable: string | undefined;
  let eventVariable: string | undefined;

  variables.forEach(variable => {
    const name = variable.columnName.toLowerCase();

    const hasSurvivalKeyword = survivalKeywords.some(keyword =>
      name.includes(keyword)
    );

    if (hasSurvivalKeyword) {
      survivalVariables.push(variable.columnName);

      // Try to identify time and event variables
      if (!timeVariable && timeKeywords.some(keyword => name.includes(keyword))) {
        timeVariable = variable.columnName;
      }

      if (!eventVariable && eventKeywords.some(keyword => name.includes(keyword))) {
        eventVariable = variable.columnName;
      }
    }
  });

  return {
    survivalVariables,
    hasSurvivalData: survivalVariables.length > 0,
    timeVariable,
    eventVariable
  };
};

/**
 * Detects if a variable is likely an identifier based on naming patterns and data characteristics
 */
export const detectIdentifierVariable = (column: Column, data: DataRow[]): boolean => {
  const columnNameLower = column.name.toLowerCase();

  // Check for common identifier naming patterns
  const idPatterns = [
    /\bid\b/i,                    // "id", "ID", "Id"
    /\bkey\b/i,                   // "key", "Key", "KEY"
    /identifier/i,                // "identifier", "Identifier", "IDENTIFIER"
    /\bnumber\b/i,                // "number", "Number", "NUMBER"
    /\bcode\b/i,                  // "code", "Code", "CODE"
    /\bref\b/i,                   // "ref", "reference", "REF"
    /\bseq\b/i,                   // "seq", "sequence", "SEQ"
    /\bindex\b/i,                 // "index", "Index", "INDEX"
    /\buuid\b/i,                  // "uuid", "UUID"
    /\bguid\b/i,                  // "guid", "GUID"
    /\bserial\b/i,                // "serial", "Serial", "SERIAL"
    /\brecord\b/i,                // "record", "Record", "RECORD"
    /\brow\b/i,                   // "row", "Row", "ROW"
    /\bpatient\b.*\bid\b/i,       // "PatientID", "patient_id"
    /\bstudent\b.*\bid\b/i,       // "StudentID", "student_id"
    /\buser\b.*\bid\b/i,          // "UserID", "user_id"
    /\bcustomer\b.*\bid\b/i,      // "CustomerID", "customer_id"
    /\bemployee\b.*\bid\b/i,      // "EmployeeID", "employee_id"
    /\bsubject\b.*\bid\b/i,       // "SubjectID", "subject_id"
    /\bcase\b.*\bid\b/i,          // "CaseID", "case_id"
    /\bparticipant\b.*\bid\b/i,   // "ParticipantID", "participant_id"
    /^id_/i,                      // "id_something", "ID_SOMETHING"
    /_id$/i,                      // "something_id", "SOMETHING_ID"
    /^.*_?id$/i,                  // Ends with "id" or "_id"
    /^.*_?key$/i,                 // Ends with "key" or "_key"
    /^.*_?num$/i,                 // Ends with "num" or "_num"
    /^.*_?no$/i,                  // Ends with "no" or "_no"
  ];

  const hasIdPattern = idPatterns.some(pattern => pattern.test(columnNameLower));

  // Get non-null values for analysis
  const values = data
    .map(row => row[column.name])
    .filter(val => val !== null && val !== undefined && String(val).trim() !== '');

  if (values.length === 0) return false;

  // Calculate uniqueness percentage
  const uniqueValues = new Set(values.map(v => String(v))).size;
  const uniquenessPercentage = (uniqueValues / values.length) * 100;

  // High uniqueness suggests identifier
  const isHighlyUnique = uniquenessPercentage >= 95;

  // Check for sequential numeric pattern (common in auto-generated IDs)
  const hasSequentialPattern = checkSequentialPattern(values);

  // Check for alphanumeric ID patterns
  const hasAlphanumericPattern = checkAlphanumericIdPattern(values);

  // Decision logic: combine multiple indicators
  // Strong indicators - definitely an identifier
  if (hasIdPattern && isHighlyUnique) return true;
  if (isHighlyUnique && values.length > 10 && (hasSequentialPattern || hasAlphanumericPattern)) return true;

  // Medium indicators - likely an identifier
  if (hasIdPattern && uniquenessPercentage >= 80) return true;
  if (hasIdPattern && values.length > 5 && uniquenessPercentage >= 70) return true;

  // Additional checks for edge cases
  if (uniquenessPercentage >= 98 && values.length > 20) return true; // Very high uniqueness
  if (hasSequentialPattern && uniquenessPercentage >= 90) return true; // Sequential with high uniqueness
  if (hasAlphanumericPattern && uniquenessPercentage >= 85) return true; // Structured IDs

  // Check for all unique values (potential identifier even without naming pattern)
  if (uniquenessPercentage === 100 && values.length > 10) return true;

  return false;
};

/**
 * Checks if values follow a sequential numeric pattern
 */
const checkSequentialPattern = (values: any[]): boolean => {
  if (values.length < 3) return false;

  const numericValues = values
    .map(v => Number(v))
    .filter(v => !isNaN(v) && Number.isInteger(v))
    .sort((a, b) => a - b);

  if (numericValues.length < values.length * 0.8) return false; // Most values should be numeric

  // Check if values are mostly sequential
  let sequentialCount = 0;
  for (let i = 1; i < numericValues.length; i++) {
    if (numericValues[i] - numericValues[i-1] === 1) {
      sequentialCount++;
    }
  }

  return sequentialCount / (numericValues.length - 1) > 0.7; // 70% sequential
};

/**
 * Checks if values follow alphanumeric ID patterns
 */
const checkAlphanumericIdPattern = (values: any[]): boolean => {
  if (values.length < 3) return false;

  const stringValues = values.map(v => String(v));

  // Common ID patterns
  const idPatterns = [
    /^[A-Z]{2,4}\d{3,}$/,        // ABC123, ABCD1234
    /^\d{3,}-[A-Z]{2,}$/,        // 123-ABC, 1234-ABCD
    /^[A-Z]\d{3,}$/,             // A123, B1234
    /^\d{8,}$/,                  // Long numeric IDs
    /^[A-Z0-9]{8,}$/,            // Mixed alphanumeric
    /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/i, // UUID format
  ];

  const matchingPatterns = stringValues.filter(value =>
    idPatterns.some(pattern => pattern.test(value))
  );

  return matchingPatterns.length / stringValues.length > 0.8; // 80% match ID patterns
};

/**
 * Performs identifier-specific analysis
 */
const analyzeIdentifierVariable = (column: Column, data: DataRow[]) => {
  const values = data.map(row => row[column.name]);
  const nonNullValues = values.filter(val => val !== null && val !== undefined && String(val).trim() !== '');

  const uniqueValues = new Set(nonNullValues.map(v => String(v))).size;
  const uniquenessPercentage = (uniqueValues / nonNullValues.length) * 100;
  const completenessPercentage = (nonNullValues.length / values.length) * 100;
  const duplicateCount = nonNullValues.length - uniqueValues;

  const hasSequentialPattern = checkSequentialPattern(nonNullValues);
  const hasAlphanumericPattern = checkAlphanumericIdPattern(nonNullValues);

  const recommendations: string[] = [];

  if (completenessPercentage < 100) {
    recommendations.push(`${(100 - completenessPercentage).toFixed(1)}% of IDs are missing - ensure all records have identifiers`);
  }

  if (duplicateCount > 0) {
    recommendations.push(`${duplicateCount} duplicate IDs found - identifiers should be unique`);
  }

  if (uniquenessPercentage < 95) {
    recommendations.push('Low uniqueness for identifier variable - verify this is intended to be an ID');
  }

  if (recommendations.length === 0) {
    recommendations.push('Identifier variable appears well-formed with good completeness and uniqueness');
  }

  return {
    uniquenessPercentage,
    hasSequentialPattern,
    hasAlphanumericPattern,
    completenessPercentage,
    duplicateCount,
    recommendations
  };
};

/**
 * Analyzes an individual variable/column
 */
const analyzeVariable = (column: Column, data: DataRow[]): VariableAnalysis => {
  const values = data.map(row => row[column.name]);
  const nonNullValues = values.filter(val => val !== null && val !== undefined && String(val).trim() !== '');
  
  const uniqueValues = new Set(nonNullValues).size;
  const missingCount = values.length - nonNullValues.length;
  const missingPercentage = (missingCount / values.length) * 100;
  
  const insights: string[] = [];
  
  let analysis: Partial<VariableAnalysis> = {};

  if (column.type === DataType.NUMERIC) {
    const numericValues = nonNullValues
      .map(val => Number(val))
      .filter(val => !isNaN(val));
    
    if (numericValues.length > 0) {
      const mean = calculateMean(numericValues);
      const median = calculateMedian(numericValues);
      const standardDeviation = calculateStandardDeviation(numericValues);
      
      analysis.mean = mean;
      analysis.median = median;
      analysis.standardDeviation = standardDeviation;
      
      // Check normality if enough data points
      if (numericValues.length >= 3) {
        const normalityTest = isNormallyDistributed(numericValues);
        analysis.isNormal = normalityTest.isNormal;
        
        if (normalityTest.isNormal) {
          insights.push("Data appears normally distributed");
        } else {
          insights.push("Data is not normally distributed");
        }
      }
      
      // Check for outliers
      try {
        const outlierReport = checkOutliers(data, column.name);
        analysis.hasOutliers = outlierReport.outliers.length > 0;
        if (analysis.hasOutliers) {
          insights.push(`${outlierReport.outliers.length} potential outliers detected`);
        }
      } catch (error) {
        // Handle outlier detection errors gracefully
      }
      
      // Add distribution insights
      if (standardDeviation > 0) {
        const cv = (standardDeviation / Math.abs(mean)) * 100;
        if (cv > 30) {
          insights.push("High variability in data");
        } else if (cv < 10) {
          insights.push("Low variability in data");
        }
      }
    }
  } else if (column.type === DataType.CATEGORICAL || column.type === DataType.ORDINAL) {
    const frequencies = calculateFrequencies(nonNullValues);
    const categories = Object.keys(frequencies);
    const categoryCount = categories.length;
    
    analysis.categories = categories;
    analysis.categoryCount = categoryCount;
    
    // Find most frequent category
    const mostFrequent = Object.entries(frequencies)
      .reduce((a, b) => frequencies[a[0]] > frequencies[b[0]] ? a : b);
    analysis.mostFrequentCategory = mostFrequent[0];
    
    // Add categorical insights
    if (categoryCount <= 2) {
      insights.push("Binary variable - suitable for chi-square or t-tests");
    } else if (categoryCount <= 5) {
      insights.push("Few categories - good for ANOVA or chi-square tests");
    } else if (categoryCount > 10) {
      insights.push("Many categories - consider grouping for analysis");
    }
    
    // Check for imbalanced categories
    const totalCount = nonNullValues.length;
    const proportions = Object.values(frequencies).map(count => count / totalCount);
    const maxProportion = Math.max(...proportions);
    if (maxProportion > 0.8) {
      insights.push("Highly imbalanced categories");
    }
  }
  
  // General insights
  if (missingPercentage > 20) {
    insights.push("High percentage of missing values - consider data cleaning");
  } else if (missingPercentage > 5) {
    insights.push("Some missing values present");
  }
  
  if (uniqueValues === values.length && missingCount === 0) {
    insights.push("All values are unique - potential identifier variable");
  }

  // Detect if this is an identifier variable
  const isIdentifier = detectIdentifierVariable(column, data);
  let identifierAnalysis;

  if (isIdentifier) {
    identifierAnalysis = analyzeIdentifierVariable(column, data);
    insights.push("Identified as identifier variable");
  }

  return {
    columnId: column.id,
    columnName: column.name,
    type: column.type,
    role: column.role,
    uniqueValues,
    missingCount,
    missingPercentage,
    isIdentifier,
    insights,
    identifierAnalysis,
    ...analysis
  };
};

/**
 * Generates intelligent analysis recommendations based on dataset characteristics
 */
const generateRecommendations = (
  dataset: Dataset,
  variableAnalysis: VariableAnalysis[],
  dataQuality: DatasetAnalysis['dataQuality']
): AnalysisRecommendation[] => {
  const recommendations: AnalysisRecommendation[] = [];

  const numericVars = variableAnalysis.filter(v => v.type === DataType.NUMERIC);
  const categoricalVars = variableAnalysis.filter(v =>
    v.type === DataType.CATEGORICAL || v.type === DataType.ORDINAL
  );
  const binaryVars = categoricalVars.filter(v => v.categoryCount === 2);

  // Data Quality Recommendations
  if (dataQuality.missingDataPercentage > 10) {
    recommendations.push({
      id: 'data-cleaning',
      title: 'Data Cleaning Required',
      description: 'Address missing values before analysis',
      reason: `${dataQuality.missingDataPercentage.toFixed(1)}% of your data contains missing values. Consider imputation or removal strategies.`,
      analysisType: 'data-preparation',
      category: 'Data Management',
      priority: 'high',
      variables: dataQuality.columnsWithMissingData,
      path: 'data-management'
    });
  }

  // Descriptive Statistics - Always recommend for new datasets
  recommendations.push({
    id: 'descriptive-stats',
    title: 'Explore Your Data with Descriptive Statistics',
    description: 'Get summary statistics and understand your data distribution',
    reason: `Start by exploring your ${dataset.data.length} observations across ${dataset.columns.length} variables.`,
    analysisType: 'descriptive',
    category: 'Descriptive Statistics',
    priority: 'high',
    variables: variableAnalysis.map(v => v.columnName),
    path: 'stats/descriptives'
  });

  // Correlation Analysis - if multiple numeric variables
  if (numericVars.length >= 2) {
    recommendations.push({
      id: 'correlation-analysis',
      title: 'Correlation Analysis',
      description: 'Examine relationships between numeric variables',
      reason: `You have ${numericVars.length} numeric variables: ${numericVars.map(v => v.columnName).join(', ')}. Explore their relationships.`,
      analysisType: 'correlation',
      category: 'Correlation Analysis',
      priority: 'medium',
      variables: numericVars.map(v => v.columnName),
      path: 'correlation-analysis/pearson'
    });
  }

  // T-test recommendations
  if (numericVars.length >= 1 && binaryVars.length >= 1) {
    const numericVar = numericVars[0];
    const binaryVar = binaryVars[0];

    recommendations.push({
      id: 'independent-t-test',
      title: 'Independent Samples T-Test',
      description: 'Compare means between two groups',
      reason: `Compare ${numericVar.columnName} between the two groups in ${binaryVar.columnName}.`,
      analysisType: 't-test',
      category: 'Inferential Statistics',
      priority: 'medium',
      variables: [numericVar.columnName, binaryVar.columnName],
      path: 'inference/ttest'
    });
  }

  // ANOVA recommendations
  if (numericVars.length >= 1 && categoricalVars.length >= 1) {
    const numericVar = numericVars[0];
    const categoricalVar = categoricalVars.find(v => v.categoryCount && v.categoryCount > 2);

    if (categoricalVar) {
      recommendations.push({
        id: 'one-way-anova',
        title: 'One-Way ANOVA',
        description: 'Compare means across multiple groups',
        reason: `Compare ${numericVar.columnName} across the ${categoricalVar.categoryCount} groups in ${categoricalVar.columnName}.`,
        analysisType: 'anova',
        category: 'Inferential Statistics',
        priority: 'medium',
        variables: [numericVar.columnName, categoricalVar.columnName],
        path: 'inference/anova'
      });
    }
  }

  // Chi-square test for categorical variables
  if (categoricalVars.length >= 2) {
    const cat1 = categoricalVars[0];
    const cat2 = categoricalVars[1];

    recommendations.push({
      id: 'chi-square-test',
      title: 'Chi-Square Test of Independence',
      description: 'Test association between categorical variables',
      reason: `Examine the relationship between ${cat1.columnName} and ${cat2.columnName}.`,
      analysisType: 'chi-square',
      category: 'Inferential Statistics',
      priority: 'medium',
      variables: [cat1.columnName, cat2.columnName],
      path: 'stats/crosstabs'
    });
  }

  // Regression analysis
  if (numericVars.length >= 2) {
    const dependentVar = numericVars.find(v => v.role === VariableRole.DEPENDENT) || numericVars[0];
    const independentVars = numericVars.filter(v => v.columnId !== dependentVar.columnId);

    if (independentVars.length > 0) {
      recommendations.push({
        id: 'linear-regression',
        title: 'Linear Regression Analysis',
        description: 'Model relationships and make predictions',
        reason: `Use ${independentVars.map(v => v.columnName).join(', ')} to predict ${dependentVar.columnName}.`,
        analysisType: 'regression',
        category: 'Advanced Analysis',
        priority: 'medium',
        variables: [dependentVar.columnName, ...independentVars.map(v => v.columnName)],
        path: 'correlation-analysis/regression'
      });
    }
  }

  // Non-parametric alternatives for non-normal data
  const nonNormalNumeric = numericVars.filter(v => v.isNormal === false);
  if (nonNormalNumeric.length > 0 && categoricalVars.length > 0) {
    recommendations.push({
      id: 'non-parametric-tests',
      title: 'Non-Parametric Tests',
      description: 'Alternative tests for non-normal data',
      reason: `Some variables (${nonNormalNumeric.map(v => v.columnName).join(', ')}) are not normally distributed. Consider non-parametric alternatives.`,
      analysisType: 'non-parametric',
      category: 'Inferential Statistics',
      priority: 'low',
      variables: nonNormalNumeric.map(v => v.columnName),
      path: 'inference/nonparametric'
    });
  }

  // Enhanced Variable Role-Based Recommendations

  // 1. Regression Analysis Based on Dependent Variable Type
  const dependentVars = variableAnalysis.filter(v => v.role === VariableRole.DEPENDENT);
  const independentVars = variableAnalysis.filter(v => v.role === VariableRole.INDEPENDENT);

  if (dependentVars.length > 0 && independentVars.length > 0) {
    const dependentVar = dependentVars[0];

    if (dependentVar.type === DataType.NUMERIC) {
      recommendations.push({
        id: 'linear-regression-role-based',
        title: 'Linear Regression Analysis',
        description: 'Model your dependent variable with independent predictors',
        reason: `${dependentVar.columnName} is marked as dependent variable. Use ${independentVars.map(v => v.columnName).join(', ')} as predictors.`,
        analysisType: 'regression',
        category: 'Advanced Analysis',
        priority: 'high',
        variables: [dependentVar.columnName, ...independentVars.map(v => v.columnName)],
        path: 'correlation/linear'
      });
    } else if (dependentVar.type === DataType.CATEGORICAL) {
      recommendations.push({
        id: 'logistic-regression-role-based',
        title: 'Logistic Regression Analysis',
        description: 'Model categorical outcomes with your predictors',
        reason: `${dependentVar.columnName} is a categorical dependent variable. Use ${independentVars.map(v => v.columnName).join(', ')} as predictors.`,
        analysisType: 'regression',
        category: 'Advanced Analysis',
        priority: 'high',
        variables: [dependentVar.columnName, ...independentVars.map(v => v.columnName)],
        path: 'correlation/logistic'
      });
    }
  }

  // 2. Factor Analysis for Scale Variables
  const scalePatterns = detectScalePatterns(variableAnalysis);
  if (scalePatterns.hasScales) {
    scalePatterns.scaleGroups.forEach(group => {
      recommendations.push({
        id: `efa-${group.prefix}`,
        title: 'Exploratory Factor Analysis (EFA)',
        description: `Explore underlying factors in ${group.prefix} scale`,
        reason: `Detected ${group.count} variables with similar naming pattern (${group.prefix}). These may represent a psychological scale or construct.`,
        analysisType: 'factor-analysis',
        category: 'Advanced Analysis',
        priority: 'medium',
        variables: group.variables,
        path: 'advanced-analysis/efa'
      });

      recommendations.push({
        id: `reliability-${group.prefix}`,
        title: 'Reliability Analysis',
        description: `Assess internal consistency of ${group.prefix} scale`,
        reason: `Check Cronbach's alpha and item-total correlations for the ${group.prefix} scale items.`,
        analysisType: 'reliability',
        category: 'Advanced Analysis',
        priority: 'medium',
        variables: group.variables,
        path: 'advanced-analysis/reliability'
      });
    });

    // CFA recommendation if multiple scales detected
    if (scalePatterns.scaleGroups.length > 1) {
      recommendations.push({
        id: 'cfa-multiple-scales',
        title: 'Confirmatory Factor Analysis (CFA)',
        description: 'Test your measurement model with multiple scales',
        reason: `Multiple scales detected (${scalePatterns.scaleGroups.map(g => g.prefix).join(', ')}). CFA can test the factor structure.`,
        analysisType: 'factor-analysis',
        category: 'Advanced Analysis',
        priority: 'low',
        variables: scalePatterns.scaleGroups.flatMap(g => g.variables),
        path: 'advanced-analysis/cfa'
      });
    }
  }

  // 3. Survival Analysis Detection
  const survivalPatterns = detectSurvivalVariables(variableAnalysis);
  if (survivalPatterns.hasSurvivalData) {
    recommendations.push({
      id: 'survival-analysis',
      title: 'Survival Analysis',
      description: 'Analyze time-to-event data',
      reason: `Detected survival-related variables: ${survivalPatterns.survivalVariables.join(', ')}. ${
        survivalPatterns.timeVariable ? `Time variable: ${survivalPatterns.timeVariable}. ` : ''
      }${
        survivalPatterns.eventVariable ? `Event variable: ${survivalPatterns.eventVariable}.` : ''
      }`,
      analysisType: 'survival',
      category: 'Advanced Analysis',
      priority: 'high',
      variables: survivalPatterns.survivalVariables,
      path: 'advanced-analysis/survival'
    });
  }

  // Visualization recommendations
  recommendations.push({
    id: 'data-visualization',
    title: 'Create Visualizations',
    description: 'Visualize your data patterns and relationships',
    reason: 'Visual exploration helps identify patterns, outliers, and relationships in your data.',
    analysisType: 'visualization',
    category: 'Data Visualization',
    priority: 'medium',
    variables: variableAnalysis.map(v => v.columnName),
    path: 'charts'
  });

  return recommendations.sort((a, b) => {
    const priorityOrder = { high: 3, medium: 2, low: 1 };
    return priorityOrder[b.priority] - priorityOrder[a.priority];
  });
};

/**
 * Detects appropriate statistical tests based on variable types and research questions
 */
export const suggestStatisticalTests = (
  dependentVar: VariableAnalysis,
  independentVars: VariableAnalysis[]
): AnalysisRecommendation[] => {
  const suggestions: AnalysisRecommendation[] = [];

  if (dependentVar.type === DataType.NUMERIC) {
    // Numeric dependent variable
    if (independentVars.length === 1) {
      const indepVar = independentVars[0];

      if (indepVar.type === DataType.NUMERIC) {
        // Numeric vs Numeric - Correlation/Regression
        suggestions.push({
          id: 'correlation-regression',
          title: 'Correlation and Regression Analysis',
          description: 'Examine linear relationship between variables',
          reason: `Both ${dependentVar.columnName} and ${indepVar.columnName} are numeric variables.`,
          analysisType: 'correlation',
          category: 'Correlation Analysis',
          priority: 'high',
          variables: [dependentVar.columnName, indepVar.columnName]
        });
      } else if (indepVar.type === DataType.CATEGORICAL) {
        // Numeric vs Categorical
        if (indepVar.categoryCount === 2) {
          suggestions.push({
            id: 'independent-t-test',
            title: 'Independent Samples T-Test',
            description: 'Compare means between two groups',
            reason: `Compare ${dependentVar.columnName} between groups in ${indepVar.columnName}.`,
            analysisType: 't-test',
            category: 'Inferential Statistics',
            priority: 'high',
            variables: [dependentVar.columnName, indepVar.columnName]
          });
        } else if (indepVar.categoryCount && indepVar.categoryCount > 2) {
          suggestions.push({
            id: 'one-way-anova',
            title: 'One-Way ANOVA',
            description: 'Compare means across multiple groups',
            reason: `Compare ${dependentVar.columnName} across ${indepVar.categoryCount} groups in ${indepVar.columnName}.`,
            analysisType: 'anova',
            category: 'Inferential Statistics',
            priority: 'high',
            variables: [dependentVar.columnName, indepVar.columnName]
          });
        }
      }
    } else if (independentVars.length > 1) {
      // Multiple independent variables
      const numericIndep = independentVars.filter(v => v.type === DataType.NUMERIC);
      const categoricalIndep = independentVars.filter(v => v.type === DataType.CATEGORICAL);

      if (numericIndep.length > 0) {
        suggestions.push({
          id: 'multiple-regression',
          title: 'Multiple Regression Analysis',
          description: 'Model with multiple predictors',
          reason: `Use multiple variables to predict ${dependentVar.columnName}.`,
          analysisType: 'regression',
          category: 'Advanced Analysis',
          priority: 'high',
          variables: [dependentVar.columnName, ...independentVars.map(v => v.columnName)]
        });
      }

      if (categoricalIndep.length >= 2) {
        suggestions.push({
          id: 'factorial-anova',
          title: 'Factorial ANOVA',
          description: 'Analyze effects of multiple categorical factors',
          reason: `Examine effects of ${categoricalIndep.map(v => v.columnName).join(' and ')} on ${dependentVar.columnName}.`,
          analysisType: 'anova',
          category: 'Inferential Statistics',
          priority: 'medium',
          variables: [dependentVar.columnName, ...categoricalIndep.map(v => v.columnName)]
        });
      }
    }
  } else if (dependentVar.type === DataType.CATEGORICAL) {
    // Categorical dependent variable
    if (independentVars.length === 1) {
      const indepVar = independentVars[0];

      if (indepVar.type === DataType.CATEGORICAL) {
        suggestions.push({
          id: 'chi-square-test',
          title: 'Chi-Square Test of Independence',
          description: 'Test association between categorical variables',
          reason: `Examine relationship between ${dependentVar.columnName} and ${indepVar.columnName}.`,
          analysisType: 'chi-square',
          category: 'Inferential Statistics',
          priority: 'high',
          variables: [dependentVar.columnName, indepVar.columnName]
        });
      } else if (indepVar.type === DataType.NUMERIC) {
        suggestions.push({
          id: 'logistic-regression',
          title: 'Logistic Regression',
          description: 'Model categorical outcomes with numeric predictors',
          reason: `Predict ${dependentVar.columnName} using ${indepVar.columnName}.`,
          analysisType: 'regression',
          category: 'Advanced Analysis',
          priority: 'medium',
          variables: [dependentVar.columnName, indepVar.columnName]
        });
      }
    }
  }

  return suggestions;
};

/**
 * Analyzes data quality and suggests preprocessing steps
 */
export const analyzeDataQuality = (dataset: Dataset): {
  issues: string[];
  recommendations: string[];
  severity: 'low' | 'medium' | 'high';
} => {
  const issues: string[] = [];
  const recommendations: string[] = [];

  const missingReport = checkMissingValues(dataset.data);
  const totalCells = dataset.data.length * dataset.columns.length;
  const missingPercentage = (missingReport.total / totalCells) * 100;

  // Missing data analysis
  if (missingPercentage > 20) {
    issues.push(`High percentage of missing data (${missingPercentage.toFixed(1)}%)`);
    recommendations.push('Consider multiple imputation or listwise deletion');
  } else if (missingPercentage > 5) {
    issues.push(`Moderate missing data (${missingPercentage.toFixed(1)}%)`);
    recommendations.push('Consider imputation strategies for missing values');
  }

  // Check for potential identifier columns
  dataset.columns.forEach(column => {
    const values = dataset.data.map(row => row[column.name]);
    const uniqueValues = new Set(values.filter(v => v !== null && v !== undefined)).size;

    if (uniqueValues === dataset.data.length) {
      issues.push(`${column.name} appears to be an identifier (all unique values)`);
      recommendations.push(`Consider excluding ${column.name} from statistical analyses`);
    }
  });

  // Check for constant variables
  dataset.columns.forEach(column => {
    const values = dataset.data.map(row => row[column.name]);
    const nonNullValues = values.filter(v => v !== null && v !== undefined);
    const uniqueValues = new Set(nonNullValues).size;

    if (uniqueValues <= 1 && nonNullValues.length > 0) {
      issues.push(`${column.name} has constant values`);
      recommendations.push(`Consider removing ${column.name} as it provides no variation`);
    }
  });

  // Determine overall severity
  let severity: 'low' | 'medium' | 'high' = 'low';
  if (missingPercentage > 20 || issues.length > 3) {
    severity = 'high';
  } else if (missingPercentage > 5 || issues.length > 1) {
    severity = 'medium';
  }

  return { issues, recommendations, severity };
};

/**
 * Smart recommendation engine that provides contextual analysis suggestions
 */
export const generateSmartRecommendations = (
  dataset: Dataset,
  userQuery?: string
): AnalysisRecommendation[] => {
  const analysis = analyzeDataset(dataset);
  const recommendations: AnalysisRecommendation[] = [];

  if (!analysis.hasData) {
    return [];
  }

  // Parse user query for intent
  const queryIntent = userQuery ? parseUserIntent(userQuery, analysis.variableAnalysis) : null;

  if (queryIntent) {
    // Generate recommendations based on user intent
    recommendations.push(...generateIntentBasedRecommendations(queryIntent, analysis));
  } else {
    // Generate general recommendations based on data characteristics
    recommendations.push(...analysis.recommendations);
  }

  return recommendations;
};

/**
 * Parses user query to understand analysis intent
 */
interface UserIntent {
  analysisType: string;
  variables: string[];
  keywords: string[];
  confidence: number;
}

const parseUserIntent = (query: string, variables: VariableAnalysis[]): UserIntent | null => {
  const queryLower = query.toLowerCase();

  // Find mentioned variables
  const mentionedVariables = variables.filter(v =>
    queryLower.includes(v.columnName.toLowerCase())
  );

  // Analysis type detection patterns
  const patterns = {
    correlation: /correlat|relationship|associat|connect/i,
    comparison: /compar|differ|between|versus|vs/i,
    prediction: /predict|forecast|model|estimate/i,
    description: /describ|summar|explor|understand/i,
    test: /test|significant|p.?value|hypothesis/i
  };

  let analysisType = 'general';
  let confidence = 0;

  for (const [type, pattern] of Object.entries(patterns)) {
    if (pattern.test(query)) {
      analysisType = type;
      confidence = 0.8;
      break;
    }
  }

  // Extract keywords
  const keywords = queryLower.split(/\s+/).filter(word => word.length > 3);

  return {
    analysisType,
    variables: mentionedVariables.map(v => v.columnName),
    keywords,
    confidence
  };
};

/**
 * Generates recommendations based on user intent
 */
const generateIntentBasedRecommendations = (
  intent: UserIntent,
  analysis: DatasetAnalysis
): AnalysisRecommendation[] => {
  const recommendations: AnalysisRecommendation[] = [];

  const mentionedVars = analysis.variableAnalysis.filter(v =>
    intent.variables.includes(v.columnName)
  );

  switch (intent.analysisType) {
    case 'correlation':
      if (mentionedVars.length >= 2) {
        const numericVars = mentionedVars.filter(v => v.type === DataType.NUMERIC);
        if (numericVars.length >= 2) {
          recommendations.push({
            id: 'intent-correlation',
            title: 'Correlation Analysis',
            description: 'Examine relationships between your specified variables',
            reason: `Based on your query, analyze correlation between ${numericVars.map(v => v.columnName).join(' and ')}.`,
            analysisType: 'correlation',
            category: 'Correlation Analysis',
            priority: 'high',
            variables: numericVars.map(v => v.columnName),
            path: 'correlation-analysis/pearson'
          });
        }
      }
      break;

    case 'comparison':
      if (mentionedVars.length >= 2) {
        const numericVar = mentionedVars.find(v => v.type === DataType.NUMERIC);
        const categoricalVar = mentionedVars.find(v =>
          v.type === DataType.CATEGORICAL || v.type === DataType.ORDINAL
        );

        if (numericVar && categoricalVar) {
          const testType = categoricalVar.categoryCount === 2 ? 't-test' : 'anova';
          const testName = categoricalVar.categoryCount === 2 ? 'T-Test' : 'ANOVA';

          recommendations.push({
            id: 'intent-comparison',
            title: `${testName} Analysis`,
            description: `Compare ${numericVar.columnName} across groups`,
            reason: `Compare ${numericVar.columnName} between groups in ${categoricalVar.columnName}.`,
            analysisType: testType,
            category: 'Inferential Statistics',
            priority: 'high',
            variables: [numericVar.columnName, categoricalVar.columnName],
            path: testType === 't-test' ? 'inference/ttest' : 'inference/anova'
          });
        }
      }
      break;

    case 'prediction':
      if (mentionedVars.length >= 2) {
        const dependentVar = mentionedVars[0]; // Assume first mentioned is dependent
        const independentVars = mentionedVars.slice(1);

        recommendations.push({
          id: 'intent-prediction',
          title: 'Regression Analysis',
          description: 'Build predictive model',
          reason: `Create a model to predict ${dependentVar.columnName} using ${independentVars.map(v => v.columnName).join(', ')}.`,
          analysisType: 'regression',
          category: 'Advanced Analysis',
          priority: 'high',
          variables: mentionedVars.map(v => v.columnName),
          path: 'correlation-analysis/regression'
        });
      }
      break;

    case 'description':
      recommendations.push({
        id: 'intent-descriptive',
        title: 'Descriptive Statistics',
        description: 'Explore and summarize your data',
        reason: mentionedVars.length > 0
          ? `Get detailed statistics for ${mentionedVars.map(v => v.columnName).join(', ')}.`
          : 'Explore your dataset with comprehensive descriptive statistics.',
        analysisType: 'descriptive',
        category: 'Descriptive Statistics',
        priority: 'high',
        variables: mentionedVars.length > 0 ? mentionedVars.map(v => v.columnName) : analysis.variableAnalysis.map(v => v.columnName),
        path: 'stats/descriptives'
      });
      break;
  }

  return recommendations;
};

/**
 * Generates contextual insights about variable relationships
 */
export const generateVariableInsights = (variables: VariableAnalysis[]): string[] => {
  const insights: string[] = [];

  const numericVars = variables.filter(v => v.type === DataType.NUMERIC);
  const categoricalVars = variables.filter(v =>
    v.type === DataType.CATEGORICAL || v.type === DataType.ORDINAL
  );

  // Insights about variable distribution
  if (numericVars.length > categoricalVars.length) {
    insights.push(`Your dataset is primarily numeric (${numericVars.length} numeric vs ${categoricalVars.length} categorical variables). Consider correlation and regression analyses.`);
  } else if (categoricalVars.length > numericVars.length) {
    insights.push(`Your dataset is primarily categorical (${categoricalVars.length} categorical vs ${numericVars.length} numeric variables). Consider chi-square tests and cross-tabulations.`);
  } else {
    insights.push(`Your dataset has a balanced mix of numeric and categorical variables. This allows for diverse analysis approaches.`);
  }

  // Insights about data quality
  const highMissingVars = variables.filter(v => v.missingPercentage > 20);
  if (highMissingVars.length > 0) {
    insights.push(`Variables with high missing data: ${highMissingVars.map(v => v.columnName).join(', ')}. Consider data cleaning before analysis.`);
  }

  // Insights about normality
  const nonNormalVars = numericVars.filter(v => v.isNormal === false);
  if (nonNormalVars.length > 0) {
    insights.push(`Non-normal distributions detected in: ${nonNormalVars.map(v => v.columnName).join(', ')}. Consider non-parametric tests or data transformation.`);
  }

  // Insights about outliers
  const outlierVars = numericVars.filter(v => v.hasOutliers);
  if (outlierVars.length > 0) {
    insights.push(`Potential outliers detected in: ${outlierVars.map(v => v.columnName).join(', ')}. Review these values before analysis.`);
  }

  return insights;
};

/**
 * Analyzes missing data patterns to determine if data is MCAR, MAR, or MNAR
 */
export const analyzeMissingDataPattern = (
  dataset: Dataset,
  variable: string
): MissingDataPattern => {
  const data = dataset.data;
  const missingIndices = data
    .map((row, index) => ({ row, index }))
    .filter(({ row }) =>
      row[variable] === null ||
      row[variable] === undefined ||
      String(row[variable]).trim() === ''
    )
    .map(({ index }) => index);

  if (missingIndices.length === 0) {
    return {
      type: 'MCAR',
      confidence: 1.0,
      description: 'No missing values detected',
      recommendation: 'No action needed for missing data'
    };
  }

  const totalRows = data.length;
  const missingPercentage = (missingIndices.length / totalRows) * 100;

  // Simple pattern analysis - in a real implementation, you'd use more sophisticated tests
  // like Little's MCAR test, but this provides a basic classification

  // Check for systematic patterns
  const isSystematic = checkSystematicMissing(data, variable, missingIndices);
  const hasRelationshipWithOtherVars = checkMissingRelationships(dataset, variable, missingIndices);

  let pattern: MissingDataPattern;

  if (missingPercentage < 5) {
    pattern = {
      type: 'MCAR',
      confidence: 0.8,
      description: `Low missing data rate (${missingPercentage.toFixed(1)}%) with no obvious patterns`,
      recommendation: 'Consider listwise deletion or simple imputation methods'
    };
  } else if (isSystematic) {
    pattern = {
      type: 'MNAR',
      confidence: 0.7,
      description: 'Missing data appears to follow systematic patterns, possibly related to the variable itself',
      recommendation: 'Consider domain knowledge for imputation or model-based approaches'
    };
  } else if (hasRelationshipWithOtherVars) {
    pattern = {
      type: 'MAR',
      confidence: 0.6,
      description: 'Missing data appears related to other observed variables',
      recommendation: 'Use multiple imputation or regression-based imputation methods'
    };
  } else {
    pattern = {
      type: 'MCAR',
      confidence: 0.5,
      description: 'Missing data appears random, but further testing recommended',
      recommendation: 'Perform Little\'s MCAR test for confirmation, consider multiple imputation'
    };
  }

  return pattern;
};

/**
 * Checks for systematic missing data patterns
 */
const checkSystematicMissing = (
  data: DataRow[],
  _variable: string,
  missingIndices: number[]
): boolean => {
  // Check if missing values cluster at beginning, end, or specific intervals
  if (missingIndices.length < 3) return false;

  // Check for clustering at start or end
  const sortedIndices = [...missingIndices].sort((a, b) => a - b);
  const firstQuarter = data.length * 0.25;
  const lastQuarter = data.length * 0.75;

  const startClustered = sortedIndices.filter(i => i < firstQuarter).length / missingIndices.length > 0.6;
  const endClustered = sortedIndices.filter(i => i > lastQuarter).length / missingIndices.length > 0.6;

  return startClustered || endClustered;
};

/**
 * Checks if missing data is related to other variables
 */
const checkMissingRelationships = (
  dataset: Dataset,
  variable: string,
  missingIndices: number[]
): boolean => {
  const data = dataset.data;
  const otherVariables = dataset.columns
    .filter(col => col.name !== variable && col.type === DataType.CATEGORICAL)
    .slice(0, 3); // Check up to 3 other categorical variables

  for (const otherVar of otherVariables) {
    const missingByCategory: Record<string, number> = {};
    const totalByCategory: Record<string, number> = {};

    data.forEach((row, index) => {
      const categoryValue = String(row[otherVar.name] || 'Unknown');
      totalByCategory[categoryValue] = (totalByCategory[categoryValue] || 0) + 1;

      if (missingIndices.includes(index)) {
        missingByCategory[categoryValue] = (missingByCategory[categoryValue] || 0) + 1;
      }
    });

    // Check if missing rates vary significantly across categories
    const missingRates = Object.keys(totalByCategory).map(cat => ({
      category: cat,
      rate: (missingByCategory[cat] || 0) / totalByCategory[cat]
    }));

    const maxRate = Math.max(...missingRates.map(r => r.rate));
    const minRate = Math.min(...missingRates.map(r => r.rate));

    if (maxRate - minRate > 0.2) { // 20% difference in missing rates
      return true;
    }
  }

  return false;
};

/**
 * Advanced outlier detection using multiple methods
 */
export const detectOutliersAdvanced = (
  data: DataRow[],
  variable: string,
  methods: ('IQR' | 'Z_SCORE' | 'MODIFIED_Z_SCORE')[] = ['IQR', 'Z_SCORE', 'MODIFIED_Z_SCORE']
): OutlierDetectionResult[] => {
  const values = data
    .map((row, index) => ({ value: row[variable], index }))
    .filter(({ value }) => typeof value === 'number' && !isNaN(value));

  if (values.length < 4) {
    return [];
  }

  const results: OutlierDetectionResult[] = [];
  const numericValues = values.map(v => v.value as number);

  // IQR Method
  if (methods.includes('IQR')) {
    const iqrResult = checkOutliers(data, variable, 1.5);
    results.push({
      method: 'IQR',
      outliers: iqrResult.outliers.map(o => ({
        rowIndex: o.rowIndex,
        value: o.value,
        severity: Math.abs(o.value as number - calculateMedian(numericValues)) >
                 2 * (iqrResult.q3 - iqrResult.q1) ? 'extreme' : 'moderate'
      })),
      threshold: 1.5,
      recommendation: iqrResult.outliers.length > 0 ?
        'Investigate outliers - they may represent data entry errors or genuine extreme values' :
        'No outliers detected using IQR method'
    });
  }

  // Z-Score Method
  if (methods.includes('Z_SCORE')) {
    const mean = calculateMean(numericValues);
    const std = calculateStandardDeviation(numericValues);
    const zScoreOutliers = values
      .map(({ value, index }) => ({
        rowIndex: index,
        value,
        zScore: Math.abs((value as number - mean) / std),
        severity: Math.abs((value as number - mean) / std) > 3 ? 'extreme' as const :
                 Math.abs((value as number - mean) / std) > 2 ? 'moderate' as const : 'mild' as const
      }))
      .filter(({ zScore }) => zScore > 2);

    results.push({
      method: 'Z_SCORE',
      outliers: zScoreOutliers,
      threshold: 2,
      recommendation: zScoreOutliers.length > 0 ?
        'Consider investigating values with |z-score| > 2, especially those > 3' :
        'No outliers detected using z-score method'
    });
  }

  // Modified Z-Score Method (using median absolute deviation)
  if (methods.includes('MODIFIED_Z_SCORE')) {
    const median = calculateMedian(numericValues);
    const deviations = numericValues.map(v => Math.abs(v - median));
    const mad = calculateMedian(deviations);
    const modifiedZScores = values
      .map(({ value, index }) => ({
        rowIndex: index,
        value,
        modifiedZScore: mad === 0 ? 0 : 0.6745 * (value as number - median) / mad,
        severity: mad === 0 ? 'mild' as const :
                 Math.abs(0.6745 * (value as number - median) / mad) > 3.5 ? 'extreme' as const :
                 Math.abs(0.6745 * (value as number - median) / mad) > 2.5 ? 'moderate' as const : 'mild' as const
      }))
      .filter(({ modifiedZScore }) => Math.abs(modifiedZScore) > 2.5);

    results.push({
      method: 'MODIFIED_Z_SCORE',
      outliers: modifiedZScores,
      threshold: 2.5,
      recommendation: modifiedZScores.length > 0 ?
        'Modified z-score method detected outliers - more robust to extreme values than standard z-score' :
        'No outliers detected using modified z-score method'
    });
  }

  return results;
};

/**
 * Comprehensive distribution assessment for variables
 */
export const assessDistribution = (
  data: DataRow[],
  column: Column
): DistributionAssessment => {
  const values = data
    .map(row => row[column.name])
    .filter(val => val !== null && val !== undefined && String(val).trim() !== '');

  if (column.type === DataType.NUMERIC) {
    const numericValues = values
      .map(val => Number(val))
      .filter(val => !isNaN(val));

    if (numericValues.length < 3) {
      return {
        normality: {
          isNormal: false,
          pValue: NaN,
          statistic: NaN,
          skewness: NaN,
          kurtosis: NaN,
          interpretation: 'Insufficient data for distribution analysis'
        },
        transformationSuggestions: []
      };
    }

    const normalityTest = isNormallyDistributed(numericValues);
    const skewness = calculateSkewness(numericValues);
    const kurtosis = calculateKurtosis(numericValues);

    let interpretation = '';
    const transformationSuggestions: string[] = [];

    if (normalityTest.isNormal) {
      interpretation = 'Data appears normally distributed - suitable for parametric tests';
    } else {
      interpretation = `Data is not normally distributed (p=${normalityTest.pValue.toFixed(4)})`;

      if (Math.abs(skewness) > 1) {
        if (skewness > 1) {
          interpretation += ' - highly right-skewed';
          transformationSuggestions.push('Consider log transformation for right skew');
        } else {
          interpretation += ' - highly left-skewed';
          transformationSuggestions.push('Consider square or exponential transformation for left skew');
        }
      } else if (Math.abs(skewness) > 0.5) {
        interpretation += skewness > 0 ? ' - moderately right-skewed' : ' - moderately left-skewed';
        transformationSuggestions.push('Consider Box-Cox transformation');
      }

      if (Math.abs(kurtosis) > 1) {
        interpretation += kurtosis > 0 ? ' with heavy tails' : ' with light tails';
      }

      if (!normalityTest.isNormal) {
        transformationSuggestions.push('Consider non-parametric tests as alternative');
      }
    }

    return {
      normality: {
        isNormal: normalityTest.isNormal,
        pValue: normalityTest.pValue,
        statistic: normalityTest.statistic,
        skewness,
        kurtosis,
        interpretation
      },
      transformationSuggestions
    };
  } else if (column.type === DataType.CATEGORICAL) {
    // Analyze categorical balance
    const frequencies = calculateFrequencies(data.map(row => row[column.name]));
    const totalCount = Object.values(frequencies).reduce((sum, count) => sum + count, 0);
    const categories = Object.keys(frequencies);

    if (categories.length < 2) {
      const singleCategory = categories[0] || 'Unknown';
      const singleCount = frequencies[singleCategory] || 0;

      return {
        normality: {
          isNormal: false,
          pValue: NaN,
          statistic: NaN,
          skewness: NaN,
          kurtosis: NaN,
          interpretation: 'Single category - no variation'
        },
        balance: {
          isBalanced: false,
          imbalanceRatio: Infinity,
          minorityClassPercentage: 0,
          majorityClassPercentage: 100,
          recommendation: 'Variable has no variation - consider removing',
          categoryDetails: {
            mostFrequent: { category: singleCategory, count: singleCount, percentage: 100 },
            leastFrequent: { category: singleCategory, count: singleCount, percentage: 100 },
            allCategories: [{ category: singleCategory, count: singleCount, percentage: 100 }]
          }
        },
        transformationSuggestions: ['Remove variable - no variation']
      };
    }

    const counts = Object.values(frequencies);
    const maxCount = Math.max(...counts);
    const minCount = Math.min(...counts);
    const imbalanceRatio = maxCount / minCount;
    const minorityClassPercentage = (minCount / totalCount) * 100;
    const majorityClassPercentage = (maxCount / totalCount) * 100;

    // Find most and least frequent categories
    const categoryEntries = Object.entries(frequencies).map(([category, count]) => ({
      category,
      count,
      percentage: (count / totalCount) * 100
    }));

    categoryEntries.sort((a, b) => b.count - a.count); // Sort by count descending

    const mostFrequent = categoryEntries[0];
    const leastFrequent = categoryEntries[categoryEntries.length - 1];

    let isBalanced = true;
    let recommendation = '';

    if (imbalanceRatio > 10) {
      isBalanced = false;
      recommendation = `Severe class imbalance detected. Leading category: '${mostFrequent.category}' (${mostFrequent.percentage.toFixed(1)}% of data), Least frequent: '${leastFrequent.category}' (${leastFrequent.percentage.toFixed(1)}% of data). Consider resampling techniques or combining rare categories.`;
    } else if (imbalanceRatio > 3) {
      isBalanced = false;
      recommendation = `Moderate class imbalance detected. Leading category: '${mostFrequent.category}' (${mostFrequent.percentage.toFixed(1)}% of data), Least frequent: '${leastFrequent.category}' (${leastFrequent.percentage.toFixed(1)}% of data). Monitor for potential bias in analyses.`;
    } else {
      recommendation = `Classes are reasonably balanced. Most frequent: '${mostFrequent.category}' (${mostFrequent.percentage.toFixed(1)}%), Least frequent: '${leastFrequent.category}' (${leastFrequent.percentage.toFixed(1)}%).`;
    }

    return {
      normality: {
        isNormal: false,
        pValue: NaN,
        statistic: NaN,
        skewness: NaN,
        kurtosis: NaN,
        interpretation: 'Categorical variable - normality not applicable'
      },
      balance: {
        isBalanced,
        imbalanceRatio,
        minorityClassPercentage,
        majorityClassPercentage,
        recommendation,
        categoryDetails: {
          mostFrequent,
          leastFrequent,
          allCategories: categoryEntries
        }
      },
      transformationSuggestions: isBalanced ? [] : [
        'Consider stratified sampling to balance categories',
        'Use appropriate statistical methods for imbalanced data',
        `Consider combining rare categories (${categoryEntries.filter(c => c.percentage < 5).map(c => c.category).join(', ')}) if meaningful`,
        'Apply class weights in machine learning models'
      ]
    };
  }

  return {
    normality: {
      isNormal: false,
      pValue: NaN,
      statistic: NaN,
      skewness: NaN,
      kurtosis: NaN,
      interpretation: 'Distribution analysis not applicable for this data type'
    },
    transformationSuggestions: []
  };
};

/**
 * Performs consistency checks on data
 */
export const performConsistencyChecks = (dataset: Dataset): ConsistencyCheck[] => {
  const checks: ConsistencyCheck[] = [];
  const data = dataset.data;

  dataset.columns.forEach(column => {
    const columnChecks: ConsistencyCheck = {
      type: 'LOGICAL',
      variable: column.name,
      issues: [],
      recommendation: ''
    };

    // Check for logical inconsistencies based on variable name patterns
    const columnNameLower = column.name.toLowerCase();

    if (column.type === DataType.NUMERIC) {
      data.forEach((row, index) => {
        const value = row[column.name];
        if (typeof value === 'number' && !isNaN(value)) {
          // Age checks
          if (columnNameLower.includes('age')) {
            if (value < 0 || value > 150) {
              columnChecks.issues.push({
                rowIndex: index,
                value,
                description: `Unrealistic age value: ${value}`,
                severity: value < 0 || value > 120 ? 'high' : 'medium'
              });
            }
          }

          // Percentage checks
          if (columnNameLower.includes('percent') || columnNameLower.includes('rate') ||
              columnNameLower.includes('ratio')) {
            if (value < 0 || value > 100) {
              columnChecks.issues.push({
                rowIndex: index,
                value,
                description: `Percentage/rate outside 0-100 range: ${value}`,
                severity: 'medium'
              });
            }
          }

          // Score checks (assuming 0-100 scale)
          if (columnNameLower.includes('score') || columnNameLower.includes('grade')) {
            if (value < 0 || value > 100) {
              columnChecks.issues.push({
                rowIndex: index,
                value,
                description: `Score outside expected range: ${value}`,
                severity: 'low'
              });
            }
          }

          // Count checks
          if (columnNameLower.includes('count') || columnNameLower.includes('number')) {
            if (value < 0) {
              columnChecks.issues.push({
                rowIndex: index,
                value,
                description: `Negative count value: ${value}`,
                severity: 'high'
              });
            }
          }
        }
      });
    }

    // Date consistency checks
    if (column.type === DataType.DATE || columnNameLower.includes('date')) {
      data.forEach((row, index) => {
        const value = row[column.name];
        if (value && typeof value !== 'boolean') {
          const dateValue = new Date(value as string | number | Date);
          const currentDate = new Date();
          const year1900 = new Date('1900-01-01');

          if (isNaN(dateValue.getTime())) {
            columnChecks.issues.push({
              rowIndex: index,
              value,
              description: `Invalid date format: ${value}`,
              severity: 'high'
            });
          } else if (dateValue > currentDate) {
            columnChecks.issues.push({
              rowIndex: index,
              value,
              description: `Future date: ${value}`,
              severity: 'medium'
            });
          } else if (dateValue < year1900) {
            columnChecks.issues.push({
              rowIndex: index,
              value,
              description: `Very old date (before 1900): ${value}`,
              severity: 'low'
            });
          }
        }
      });
    }

    if (columnChecks.issues.length > 0) {
      const highSeverityCount = columnChecks.issues.filter(i => i.severity === 'high').length;
      const mediumSeverityCount = columnChecks.issues.filter(i => i.severity === 'medium').length;

      if (highSeverityCount > 0) {
        columnChecks.recommendation = `Critical: ${highSeverityCount} high-severity issues found. Review and correct these values.`;
      } else if (mediumSeverityCount > 0) {
        columnChecks.recommendation = `${mediumSeverityCount} medium-severity issues found. Verify these values are correct.`;
      } else {
        columnChecks.recommendation = `${columnChecks.issues.length} minor issues found. Consider reviewing for data quality.`;
      }

      checks.push(columnChecks);
    }
  });

  return checks;
};

/**
 * Assesses sample size adequacy for common statistical tests
 */
export const assessSampleSizeAdequacy = (dataset: Dataset): SampleSizeAssessment => {
  const sampleSize = dataset.data.length;
  const numVariables = dataset.columns.length;

  // Basic rules of thumb for different analyses
  let recommendedMinimum = 30; // Basic minimum for most statistical tests
  const recommendations: string[] = [];

  // Adjust based on number of variables and analysis types
  if (numVariables > 10) {
    recommendedMinimum = Math.max(recommendedMinimum, numVariables * 10); // 10 observations per variable
    recommendations.push('For multiple regression: aim for 10-15 observations per predictor variable');
  }

  // Factor analysis rule
  if (numVariables >= 5) {
    const factorAnalysisMin = numVariables * 5;
    recommendedMinimum = Math.max(recommendedMinimum, factorAnalysisMin);
    recommendations.push('For factor analysis: minimum 5 observations per variable, preferably 10+');
  }

  // Check for categorical variables with multiple levels
  const categoricalVars = dataset.columns.filter(col => col.type === DataType.CATEGORICAL);
  categoricalVars.forEach(col => {
    const frequencies = calculateFrequencies(
      dataset.data.map(row => row[col.name])
    );
    const categories = Object.keys(frequencies);
    if (categories.length > 2) {
      const minPerCategory = Math.min(...Object.values(frequencies));
      if (minPerCategory < 5) {
        recommendations.push(`Variable "${col.name}" has categories with <5 observations - consider combining categories`);
      }
    }
  });

  const isAdequate = sampleSize >= recommendedMinimum;

  if (!isAdequate) {
    recommendations.push(`Current sample size (${sampleSize}) is below recommended minimum (${recommendedMinimum})`);
    recommendations.push('Consider collecting more data or using simpler analytical approaches');
  } else {
    recommendations.push('Sample size appears adequate for planned analyses');
  }

  return {
    isAdequate,
    currentSize: sampleSize,
    recommendedMinimum,
    recommendations
  };
};

/**
 * Validates data types and suggests corrections
 */
export const validateDataTypes = (dataset: Dataset): Array<{
  variable: string;
  currentType: DataType;
  suggestedType: DataType;
  confidence: number;
  reason: string;
}> => {
  const suggestions: Array<{
    variable: string;
    currentType: DataType;
    suggestedType: DataType;
    confidence: number;
    reason: string;
  }> = [];

  dataset.columns.forEach(column => {
    const values = dataset.data
      .map(row => row[column.name])
      .filter(val => val !== null && val !== undefined && String(val).trim() !== '');

    if (values.length === 0) return;

    const detectedType = detectDataType(values);

    if (detectedType !== column.type) {
      let confidence = 0.7;
      let reason = '';
      let shouldSuggest = true;

      // Add safeguards to prevent false positives
      const columnNameLower = column.name.toLowerCase();

      // Don't suggest changing obviously numeric variables to dates
      if (column.type === DataType.NUMERIC && detectedType === DataType.DATE) {
        const numericKeywords = ['age', 'score', 'count', 'number', 'amount', 'value', 'price', 'cost', 'weight', 'height', 'length', 'width', 'depth', 'size', 'quantity', 'percent', 'rate', 'ratio', 'index', 'level', 'grade', 'rank'];
        if (numericKeywords.some(keyword => columnNameLower.includes(keyword))) {
          shouldSuggest = false; // Skip this suggestion as it's likely a false positive
        } else {
          // Additional check: if all values are small integers, probably not dates
          const allSmallIntegers = values.every(v => {
            const num = Number(v);
            return Number.isInteger(num) && num >= 0 && num <= 200;
          });
          if (allSmallIntegers) {
            shouldSuggest = false;
          } else {
            confidence = 0.4; // Lower confidence for numeric to date suggestions
            reason = 'Values could be timestamps or date codes - verify if this represents dates';
          }
        }
      }

      // Increase confidence based on specific patterns
      if (column.type === DataType.TEXT && detectedType === DataType.CATEGORICAL) {
        const uniqueValues = new Set(values).size;
        if (uniqueValues <= 10 && uniqueValues < values.length * 0.5) {
          confidence = 0.9;
          reason = `Only ${uniqueValues} unique values out of ${values.length} observations - likely categorical`;
        }
      } else if (column.type === DataType.CATEGORICAL && detectedType === DataType.NUMERIC) {
        const allNumeric = values.every(v => !isNaN(Number(v)));
        if (allNumeric) {
          confidence = 0.8;
          reason = 'All values are numeric - consider if this should be a numeric variable';
        }
      } else if (column.type === DataType.TEXT && detectedType === DataType.BOOLEAN) {
        confidence = 0.95;
        reason = 'Values appear to be boolean (true/false, yes/no, 0/1)';
      } else if (column.type === DataType.TEXT && detectedType === DataType.DATE) {
        // Check if values actually look like dates
        const datePatterns = values.some(v => {
          const str = String(v);
          return str.includes('/') || str.includes('-') || /\d{4}/.test(str);
        });
        if (datePatterns) {
          confidence = 0.8;
          reason = 'Values appear to contain date patterns';
        } else {
          shouldSuggest = false;
        }
      }

      if (shouldSuggest) {
        suggestions.push({
          variable: column.name,
          currentType: column.type,
          suggestedType: detectedType,
          confidence,
          reason: reason || `Detected type (${detectedType}) differs from current type (${column.type})`
        });
      }
    }
  });

  return suggestions;
};

/**
 * Comprehensive data quality assessment
 */
export const performComprehensiveDataQualityAssessment = (dataset: Dataset): DataQualityAssessment => {
  let overallScore = 100;
  const prioritizedRecommendations: DataQualityAssessment['prioritizedRecommendations'] = [];

  // First, identify all variables and separate identifiers from analytical variables
  const allVariables = dataset.columns.map(column => analyzeVariable(column, dataset.data));
  const identifierVariables = allVariables.filter(v => v.isIdentifier);
  const analyticalVariables = allVariables.filter(v => !v.isIdentifier);

  // Identifier variable analysis
  const identifierAnalysis = identifierVariables.map(variable => ({
    variable: variable.columnName,
    analysis: variable.identifierAnalysis!
  }));

  // Add identifier-specific recommendations
  identifierVariables.forEach(variable => {
    const analysis = variable.identifierAnalysis!;

    if (analysis.duplicateCount > 0) {
      prioritizedRecommendations.push({
        priority: 'high',
        category: 'identifiers',
        title: `Duplicate IDs in ${variable.columnName}`,
        description: `${analysis.duplicateCount} duplicate identifiers found`,
        actionSteps: [
          'Review data collection process for duplicate entries',
          'Implement unique constraints for identifier fields',
          'Consider adding sequence numbers to distinguish duplicates'
        ],
        impact: 'Duplicate IDs can cause data integrity issues and analysis errors'
      });
      overallScore -= 15;
    }

    if (analysis.completenessPercentage < 95) {
      prioritizedRecommendations.push({
        priority: 'medium',
        category: 'identifiers',
        title: `Missing IDs in ${variable.columnName}`,
        description: `${(100 - analysis.completenessPercentage).toFixed(1)}% of identifiers are missing`,
        actionSteps: [
          'Ensure all records have unique identifiers',
          'Implement data validation rules for required ID fields',
          'Review data entry procedures'
        ],
        impact: 'Missing identifiers can make data tracking and linking difficult'
      });
      overallScore -= 10;
    }
  });

  // Missing data analysis (excluding identifiers)
  const missingDataAnalysis = {
    totalMissing: 0,
    missingPercentage: 0,
    patternsByVariable: {} as Record<string, MissingDataPattern>,
    overallPattern: {
      type: 'MCAR' as const,
      confidence: 1.0,
      description: 'No missing data detected',
      recommendation: 'No action needed'
    },
    excludedIdentifiers: identifierVariables.map(v => v.columnName)
  };

  // Calculate missing data only for analytical variables (excluding identifiers)
  const analyticalColumns = dataset.columns.filter(col =>
    !identifierVariables.some(id => id.columnName === col.name)
  );

  const missingReport = checkMissingValues(dataset.data, analyticalColumns.map(col => col.name));
  missingDataAnalysis.totalMissing = missingReport.total;
  missingDataAnalysis.missingPercentage = (missingReport.total / (dataset.data.length * analyticalColumns.length)) * 100;

  // Analyze missing patterns for each analytical variable
  analyticalColumns.forEach(column => {
    if (missingReport.byColumn[column.name] > 0) {
      missingDataAnalysis.patternsByVariable[column.name] = analyzeMissingDataPattern(dataset, column.name);
    }
  });

  // Determine overall missing data pattern
  if (missingDataAnalysis.missingPercentage > 20) {
    overallScore -= 30;
    prioritizedRecommendations.push({
      priority: 'critical',
      category: 'missing_data',
      title: 'High Missing Data Rate',
      description: `${missingDataAnalysis.missingPercentage.toFixed(1)}% of data is missing`,
      actionSteps: [
        'Investigate reasons for missing data',
        'Consider multiple imputation methods',
        'Evaluate if data collection can be improved'
      ],
      impact: 'Severely limits analysis validity and statistical power'
    });
  } else if (missingDataAnalysis.missingPercentage > 5) {
    overallScore -= 15;
    prioritizedRecommendations.push({
      priority: 'high',
      category: 'missing_data',
      title: 'Moderate Missing Data',
      description: `${missingDataAnalysis.missingPercentage.toFixed(1)}% of data is missing`,
      actionSteps: [
        'Analyze missing data patterns',
        'Consider appropriate imputation methods',
        'Document missing data handling in analysis'
      ],
      impact: 'May affect analysis results and reduce statistical power'
    });
  }

  // Outlier analysis (excluding identifiers)
  const outlierAnalysis: Record<string, OutlierDetectionResult[]> = {};
  dataset.columns
    .filter(col => col.type === DataType.NUMERIC)
    .filter(col => !identifierVariables.some(id => id.columnName === col.name))
    .forEach(column => {
      outlierAnalysis[column.name] = detectOutliersAdvanced(dataset.data, column.name);

      const totalOutliers = outlierAnalysis[column.name].reduce((sum, result) => sum + result.outliers.length, 0);
      const outlierPercentage = (totalOutliers / dataset.data.length) * 100;

      if (outlierPercentage > 10) {
        overallScore -= 10;
        prioritizedRecommendations.push({
          priority: 'medium',
          category: 'outliers',
          title: `High Outlier Rate in ${column.name}`,
          description: `${outlierPercentage.toFixed(1)}% of values are outliers`,
          actionSteps: [
            'Investigate outlier values for data entry errors',
            'Consider outlier treatment methods',
            'Document outlier handling decisions'
          ],
          impact: 'May skew results and affect statistical assumptions'
        });
      }
    });

  // Distribution analysis (excluding identifiers)
  const distributionAnalysis: Record<string, DistributionAssessment> = {};
  dataset.columns
    .filter(col => !identifierVariables.some(id => id.columnName === col.name))
    .forEach(column => {
      distributionAnalysis[column.name] = assessDistribution(dataset.data, column);

      const dist = distributionAnalysis[column.name];
      if (column.type === DataType.NUMERIC && !dist.normality.isNormal && dist.transformationSuggestions.length > 0) {
        prioritizedRecommendations.push({
          priority: 'medium',
          category: 'distribution',
          title: `Non-normal Distribution: ${column.name}`,
          description: dist.normality.interpretation,
          actionSteps: dist.transformationSuggestions,
          impact: 'May violate assumptions of parametric statistical tests'
        });
      }

      if (dist.balance && !dist.balance.isBalanced) {
        const priority = dist.balance.imbalanceRatio > 10 ? 'high' : 'medium';
        const mostFreq = dist.balance.categoryDetails.mostFrequent;
        const leastFreq = dist.balance.categoryDetails.leastFrequent;

        prioritizedRecommendations.push({
          priority,
          category: 'distribution',
          title: `Class Imbalance: ${column.name}`,
          description: `Leading category: '${mostFreq.category}' (${mostFreq.percentage.toFixed(1)}% of data), Least frequent: '${leastFreq.category}' (${leastFreq.percentage.toFixed(1)}% of data). Imbalance ratio: ${dist.balance.imbalanceRatio.toFixed(1)}:1`,
          actionSteps: [
            `Consider combining rare categories: ${dist.balance.categoryDetails.allCategories.filter(c => c.percentage < 5).map(c => `'${c.category}'`).join(', ')}`,
            'Apply stratified sampling to balance categories',
            'Use class weights in statistical models',
            'Consider oversampling minority classes or undersampling majority class'
          ],
          impact: `May bias analysis results toward majority class '${mostFreq.category}' and reduce statistical power for rare categories`
        });
        if (priority === 'high') overallScore -= 15;
        else overallScore -= 8;
      }
    });

  // Consistency checks (excluding identifiers)
  const consistencyChecks = performConsistencyChecks(dataset).filter(check =>
    !identifierVariables.some(id => id.columnName === check.variable)
  );
  consistencyChecks.forEach(check => {
    const highSeverityIssues = check.issues.filter(i => i.severity === 'high').length;
    const mediumSeverityIssues = check.issues.filter(i => i.severity === 'medium').length;

    if (highSeverityIssues > 0) {
      overallScore -= 20;
      prioritizedRecommendations.push({
        priority: 'critical',
        category: 'consistency',
        title: `Data Consistency Issues: ${check.variable}`,
        description: `${highSeverityIssues} critical consistency issues found`,
        actionSteps: [
          'Review and correct invalid values',
          'Implement data validation rules',
          'Check data entry procedures'
        ],
        impact: 'Invalid data can lead to incorrect analysis results'
      });
    } else if (mediumSeverityIssues > 0) {
      overallScore -= 10;
      prioritizedRecommendations.push({
        priority: 'high',
        category: 'consistency',
        title: `Potential Data Issues: ${check.variable}`,
        description: `${mediumSeverityIssues} potential issues found`,
        actionSteps: [
          'Verify questionable values',
          'Document any intentional unusual values'
        ],
        impact: 'May indicate data quality problems'
      });
    }
  });

  // Data type validation (excluding identifiers)
  const dataTypeValidation = validateDataTypes(dataset).filter(validation =>
    !identifierVariables.some(id => id.columnName === validation.variable)
  );
  dataTypeValidation.forEach(suggestion => {
    if (suggestion.confidence > 0.8) {
      prioritizedRecommendations.push({
        priority: 'medium',
        category: 'data_types',
        title: `Data Type Mismatch: ${suggestion.variable}`,
        description: suggestion.reason,
        actionSteps: [
          `Consider changing from ${suggestion.currentType} to ${suggestion.suggestedType}`,
          'Verify the intended data type for analysis'
        ],
        impact: 'Incorrect data types can limit analysis options'
      });
      overallScore -= 5;
    }
  });

  // Sample size assessment
  const sampleSizeAssessment = assessSampleSizeAdequacy(dataset);
  if (!sampleSizeAssessment.isAdequate) {
    overallScore -= 25;
    prioritizedRecommendations.push({
      priority: 'high',
      category: 'sample_size',
      title: 'Insufficient Sample Size',
      description: `Current: ${sampleSizeAssessment.currentSize}, Recommended: ${sampleSizeAssessment.recommendedMinimum}`,
      actionSteps: sampleSizeAssessment.recommendations,
      impact: 'Reduces statistical power and generalizability of results'
    });
  }

  // Determine overall grade
  let overallGrade: 'excellent' | 'good' | 'fair' | 'poor';
  if (overallScore >= 90) overallGrade = 'excellent';
  else if (overallScore >= 75) overallGrade = 'good';
  else if (overallScore >= 60) overallGrade = 'fair';
  else overallGrade = 'poor';

  // Sort recommendations by priority
  const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
  prioritizedRecommendations.sort((a, b) => priorityOrder[a.priority] - priorityOrder[b.priority]);

  return {
    overallScore: Math.max(0, overallScore),
    overallGrade,
    identifierVariables: identifierAnalysis,
    missingDataAnalysis,
    outlierAnalysis,
    distributionAnalysis,
    consistencyChecks,
    dataTypeValidation,
    sampleSizeAssessment,
    prioritizedRecommendations
  };
};

/**
 * Generates a comprehensive data quality report
 */
export const generateDataQualityReport = (dataset: Dataset): {
  overall: 'excellent' | 'good' | 'fair' | 'poor';
  score: number;
  issues: Array<{
    type: 'missing' | 'outliers' | 'constant' | 'unique' | 'distribution';
    severity: 'high' | 'medium' | 'low';
    variable: string;
    description: string;
    recommendation: string;
  }>;
  summary: string;
} => {
  const issues: Array<{
    type: 'missing' | 'outliers' | 'constant' | 'unique' | 'distribution';
    severity: 'high' | 'medium' | 'low';
    variable: string;
    description: string;
    recommendation: string;
  }> = [];

  const analysis = analyzeDataset(dataset);
  let score = 100;

  if (!analysis.hasData) {
    return {
      overall: 'poor',
      score: 0,
      issues: [],
      summary: 'No data available for quality assessment.'
    };
  }

  // Check each variable for quality issues
  analysis.variableAnalysis.forEach(variable => {
    // Missing data issues
    if (variable.missingPercentage > 30) {
      issues.push({
        type: 'missing',
        severity: 'high',
        variable: variable.columnName,
        description: `${variable.missingPercentage.toFixed(1)}% missing values`,
        recommendation: 'Consider imputation or removal of this variable'
      });
      score -= 15;
    } else if (variable.missingPercentage > 10) {
      issues.push({
        type: 'missing',
        severity: 'medium',
        variable: variable.columnName,
        description: `${variable.missingPercentage.toFixed(1)}% missing values`,
        recommendation: 'Consider imputation strategies'
      });
      score -= 8;
    } else if (variable.missingPercentage > 5) {
      issues.push({
        type: 'missing',
        severity: 'low',
        variable: variable.columnName,
        description: `${variable.missingPercentage.toFixed(1)}% missing values`,
        recommendation: 'Monitor missing data patterns'
      });
      score -= 3;
    }

    // Outlier issues
    if (variable.hasOutliers) {
      issues.push({
        type: 'outliers',
        severity: 'medium',
        variable: variable.columnName,
        description: 'Potential outliers detected',
        recommendation: 'Review outliers - consider transformation or removal'
      });
      score -= 5;
    }

    // Constant variables
    if (variable.uniqueValues <= 1) {
      issues.push({
        type: 'constant',
        severity: 'high',
        variable: variable.columnName,
        description: 'Variable has constant values',
        recommendation: 'Remove this variable as it provides no information'
      });
      score -= 20;
    }

    // Potential identifier variables
    if (variable.uniqueValues === dataset.data.length && variable.missingCount === 0) {
      issues.push({
        type: 'unique',
        severity: 'low',
        variable: variable.columnName,
        description: 'All values are unique (potential ID variable)',
        recommendation: 'Consider excluding from statistical analyses'
      });
      score -= 2;
    }

    // Distribution issues for numeric variables
    if (variable.type === 'numeric' && variable.isNormal === false) {
      issues.push({
        type: 'distribution',
        severity: 'low',
        variable: variable.columnName,
        description: 'Non-normal distribution',
        recommendation: 'Consider transformation or non-parametric tests'
      });
      score -= 3;
    }
  });

  // Ensure score doesn't go below 0
  score = Math.max(0, score);

  // Determine overall quality
  let overall: 'excellent' | 'good' | 'fair' | 'poor';
  if (score >= 90) overall = 'excellent';
  else if (score >= 75) overall = 'good';
  else if (score >= 50) overall = 'fair';
  else overall = 'poor';

  // Generate summary
  const highIssues = issues.filter(i => i.severity === 'high').length;
  const mediumIssues = issues.filter(i => i.severity === 'medium').length;
  const lowIssues = issues.filter(i => i.severity === 'low').length;

  let summary = `Data quality score: ${score}/100 (${overall}). `;
  if (highIssues > 0) {
    summary += `${highIssues} critical issue${highIssues > 1 ? 's' : ''} require immediate attention. `;
  }
  if (mediumIssues > 0) {
    summary += `${mediumIssues} moderate issue${mediumIssues > 1 ? 's' : ''} should be addressed. `;
  }
  if (lowIssues > 0) {
    summary += `${lowIssues} minor issue${lowIssues > 1 ? 's' : ''} noted. `;
  }
  if (issues.length === 0) {
    summary += 'No significant data quality issues detected.';
  }

  return { overall, score, issues, summary };
};
