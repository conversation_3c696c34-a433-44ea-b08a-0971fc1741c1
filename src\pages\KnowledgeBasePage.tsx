import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Box,
  Chip,
  Avatar,
  CardHeader,
  Divider,
  Tooltip,
  IconButton,
  useTheme,
  alpha,
  Paper,
  Container,
  Breadcrumbs,
  Link,
} from '@mui/material';
import {
  School as TutorialIcon, // Using School icon for tutorials
  Info as InfoIcon,
  Launch as LaunchIcon,
} from '@mui/icons-material';
import { Link as RouterLink } from 'react-router-dom'; // Use RouterLink for navigation

// Define the structure for a tutorial option
interface TutorialOption {
  id: string; // Unique identifier
  name: string;
  shortDescription: string;
  detailedDescription: string;
  filePath: string; // Path to the markdown file
  icon: React.ReactNode;
  category: 'Statistics' | 'Data Management' | 'Visualization' | 'EpiCalc' | 'SampleSize' | 'Publication Ready' | 'Other'; // Define tutorial categories
  color: string;
}

// Define the list of available tutorials
// This list will grow as more tutorials are added
export const tutorialOptions: TutorialOption[] = [
  {
    id: 'logistic-regression',
    name: 'Logistic Regression Tutorial',
    shortDescription: 'Understand and use the Logistic Regression analysis feature.',
    detailedDescription: 'A comprehensive guide covering the basics of logistic regression, how to use the component, computational details, formula explanations, and interpretation of results.',
    filePath: 'src/docs/tutorials/logistic-regression-tutorial.md', // Path to the markdown file
    icon: <TutorialIcon />,
    category: 'Statistics',
    color: '#FF9800', // Orange
  },
  // Add more tutorials here as they are created
];

const KnowledgeBasePage: React.FC = () => {
  const theme = useTheme();
  const [selectedCategory, setSelectedCategory] = useState<string>('All');

  // Dynamically get categories from tutorial options
  const categories = ['All', ...Array.from(new Set(tutorialOptions.map(option => option.category)))].sort();

  const filteredOptions = selectedCategory === 'All'
    ? tutorialOptions
    : tutorialOptions.filter(option => option.category === selectedCategory);

  // Function to get category icon (can be expanded later if needed)
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Statistics': return <TutorialIcon />;
      // Add cases for other categories if they need different icons
      default: return <TutorialIcon />;
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Breadcrumbs navigation */}
      <Breadcrumbs sx={{ mb: 2 }}>
        <Link component={RouterLink} to="/" color="inherit">
          Home
        </Link>
        <Typography color="text.primary">Knowledge Base</Typography>
      </Breadcrumbs>

      {/* Header Section */}
      <Paper
        elevation={0}
        sx={{
          p: 4,
          mb: 4,
          background: `linear-gradient(135deg, ${alpha(theme.palette.success.main, 0.1)} 0%, ${alpha(theme.palette.info.main, 0.1)} 100%)`,
          borderRadius: 2
        }}
      >
        <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
          Knowledge Base & Tutorials
        </Typography>
        <Typography variant="h6" color="text.secondary" paragraph>
          Learn how to use the application's features with detailed tutorials
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Browse our collection of guides and tutorials covering various statistical analyses, data management techniques, and more.
        </Typography>
      </Paper>

      {/* Category Filter */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          Filter by Category
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {categories.map((category) => (
            <Chip
              key={category}
              label={category}
              onClick={() => setSelectedCategory(category)}
              variant={selectedCategory === category ? 'filled' : 'outlined'}
              color={selectedCategory === category ? 'primary' : 'default'}
              icon={category !== 'All' ? getCategoryIcon(category) : undefined}
              sx={{
                '&:hover': {
                  backgroundColor: selectedCategory === category
                    ? theme.palette.primary.dark
                    : alpha(theme.palette.primary.main, 0.1)
                }
              }}
            />
          ))}
        </Box>
      </Box>

      {/* Tutorials Grid */}
      <Grid container spacing={3}>
        {filteredOptions.map((option) => (
          <Grid item xs={12} md={6} lg={4} key={option.id}>
            <Card
              elevation={2}
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'all 0.3s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: theme.shadows[8],
                  '& .launch-button': {
                    backgroundColor: option.color,
                    color: 'white',
                  }
                }
              }}
            >
              <CardHeader
                avatar={
                  <Avatar
                    sx={{
                      bgcolor: option.color,
                      width: 48,
                      height: 48,
                    }}
                  >
                    {option.icon}
                  </Avatar>
                }
                title={
                  <Typography variant="h6" fontWeight="bold">
                    {option.name}
                  </Typography>
                }
                subheader={
                  <Box sx={{ display: 'flex', gap: 1, mt: 1, flexWrap: 'wrap' }}>
                    <Chip
                      label={option.category}
                      size="small"
                      variant="outlined"
                      color="primary"
                    />
                  </Box>
                }
                // Action button for more info could be added here if needed
              />

              <CardContent sx={{ flexGrow: 1, pt: 0 }}>
                <Typography variant="body2" color="text.secondary" paragraph>
                  {option.shortDescription}
                </Typography>

                <Typography variant="body2" paragraph>
                  {option.detailedDescription}
                </Typography>
              </CardContent>

              <Box sx={{ p: 2, pt: 0 }}>
                {/* Link to the tutorial page - needs a route and a component to render markdown */}
                <Button
                  className="launch-button"
                  variant="outlined"
                  fullWidth
                  component={RouterLink} // Use RouterLink for navigation
                  to={`/knowledge-base/${option.id}`} // Example route structure
                  endIcon={<LaunchIcon />}
                  sx={{
                    borderColor: option.color,
                    color: option.color,
                    fontWeight: 'bold',
                    '&:hover': {
                      borderColor: option.color,
                    }
                  }}
                >
                  Read Tutorial
                </Button>
              </Box>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Placeholder for future help section */}
      {/*
      <Paper
        elevation={1}
        sx={{
          p: 3,
          mt: 4,
          backgroundColor: alpha(theme.palette.info.main, 0.05),
          border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
          <InfoIcon color="info" />
          <Box>
            <Typography variant="h6" gutterBottom color="info.main">
              Need More Help?
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Explore additional resources or contact support for assistance.
            </Typography>
          </Box>
        </Box>
      </Paper>
      */}
    </Container>
  );
};

export default KnowledgeBasePage;
