import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import {
  Container,
  Typography,
  Paper,
  Box,
  CircularProgress,
  Breadcrumbs,
  Link,
  useTheme,
  alpha,
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import { unified } from 'unified'; // Import unified
import remarkParse from 'remark-parse'; // Import remark-parse
import remarkGfm from 'remark-gfm'; // Import remark-gfm
import remarkMath from 'remark-math'; // Import remark-math
import rehypeStringify from 'rehype-stringify'; // Import rehype-stringify
import rehypeKatex from 'rehype-katex'; // Import rehype-katex
import remarkRehype from 'remark-rehype'; // Import remark-rehype
import { tutorialOptions } from './KnowledgeBasePage';

// Import the markdown files directly
import logisticRegressionTutorialContent from '../docs/tutorials/logistic-regression-tutorial.md?raw';

// Map tutorial IDs to imported content
const tutorialContents: { [key: string]: string } = {
  'logistic-regression': logisticRegressionTutorialContent,
  // Add other tutorial imports here as they are added to tutorialOptions
};

// Add CSS for markdown styling
const markdownStyles = `
  .markdown-content h1 {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 1rem;
    margin-top: 1.5rem;
    color: #1976d2;
  }
  .markdown-content h2 {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 0.75rem;
    margin-top: 1.5rem;
    color: #0d47a1;
  }
  .markdown-content h3 {
    font-size: 1.25rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    margin-top: 1.25rem;
  }
  .markdown-content p {
    margin-bottom: 1rem;
    line-height: 1.6;
  }
  .markdown-content ul, .markdown-content ol {
    margin-bottom: 1rem;
    padding-left: 2rem;
  }
  .markdown-content li {
    margin-bottom: 0.5rem;
  }
  .markdown-content table {
    border-collapse: collapse;
    width: 100%;
    margin-bottom: 1rem;
  }
  .markdown-content th, .markdown-content td {
    border: 1px solid #ddd;
    padding: 0.75rem;
  }
  .markdown-content th {
    background-color: #f8f8f8;
  }
  .markdown-content pre {
    background-color: #f5f5f5;
    padding: 1rem;
    border-radius: 4px;
    overflow-x: auto;
    margin-bottom: 1rem;
  }
  .markdown-content code {
    font-family: monospace;
    background-color: #f5f5f5;
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-size: 0.9em;
  }
  .markdown-content blockquote {
    border-left: 4px solid #ddd;
    padding-left: 1rem;
    margin-left: 0;
    margin-right: 0;
    font-style: italic;
  }
`;

const TutorialPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [processedContent, setProcessedContent] = useState<string>(''); // State for processed HTML
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const theme = useTheme();

  // Find the tutorial option that matches the ID
  const tutorialOption = tutorialOptions.find(option => option.id === id);

  useEffect(() => {
    const loadAndProcessTutorial = async () => {
      if (!tutorialOption) {
        setError('Tutorial not found');
        setLoading(false);
        return;
      }

      const markdownContent = tutorialContents[tutorialOption.id];

      if (markdownContent === undefined) {
         setError('Tutorial content not found for this ID.');
         setLoading(false);
         return;
      }

      try {
        // Process markdown using unified
        const file = await unified()
          .use(remarkParse) // Parse markdown
          .use(remarkGfm) // Support GFM (tables, footnotes, strikethrough)
          .use(remarkMath) // Support math syntax
          .use(remarkRehype) // Convert markdown to HTML syntax tree
          .use(rehypeKatex) // Render math with KaTeX
          .use(rehypeStringify) // Serialize HTML syntax tree to HTML string
          .process(markdownContent);

        setProcessedContent(String(file)); // Set the processed HTML content
        setLoading(false);
      } catch (err) {
        console.error('Error processing markdown:', err);
        setError('Failed to process tutorial content.');
        setLoading(false);
      }
    };

    loadAndProcessTutorial();
  }, [id, tutorialOption]);

  // Effect to clean up duplicate raw LaTeX after rendering
  useEffect(() => {
    if (processedContent) {
      const markdownBox = document.querySelector('.markdown-content');
      if (markdownBox) { // Add null check for markdownBox
        // Find all KaTeX spans
        const katexSpans = markdownBox.querySelectorAll('.katex');

        katexSpans.forEach(span => {
          let currentNode: ChildNode | null | undefined = span.nextSibling;

          // Iterate through sibling nodes to find and remove raw LaTeX
          while (currentNode) {
            // Check if it's a text node and contains the raw LaTeX
            // This is a simplified check and might need refinement
            if (currentNode.nodeType === Node.TEXT_NODE && currentNode.textContent && currentNode.textContent.includes('== $0')) {
               if (markdownBox && currentNode) { // Add null checks before removing
                 (markdownBox as Element).removeChild(currentNode);
               }
               // Since we removed a node, the next sibling is now the original next-next sibling
               currentNode = span.nextSibling; // Re-evaluate next sibling from the span
            } else if (currentNode.nodeType === Node.ELEMENT_NODE && (currentNode as Element).textContent && (currentNode as Element).textContent.includes('== $0')) {
               // Also check if it's an element node (like a span or p) containing the raw LaTeX
               if (markdownBox && currentNode) { // Add null checks before removing
                 (markdownBox as Element).removeChild(currentNode);
               }
               currentNode = span.nextSibling; // Re-evaluate next sibling from the span
            }
            else {
              // Move to the next sibling if no raw LaTeX was found in the current node
              currentNode = currentNode.nextSibling;
            }
          }
        });
      }
    }
  }, [processedContent]); // Rerun effect when processedContent changes


  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4, display: 'flex', justifyContent: 'center' }}>
        <CircularProgress />
      </Container>
    );
  }

  if (error || !tutorialOption) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Paper sx={{ p: 4, bgcolor: alpha(theme.palette.error.main, 0.1) }}>
          <Typography variant="h5" color="error" gutterBottom>
            Error
          </Typography>
          <Typography variant="body1">
            {error || 'Tutorial not found. Please return to the Knowledge Base and try again.'}
          </Typography>
          <Box sx={{ mt: 2 }}>
            <Link component={RouterLink} to="/knowledge-base" color="primary">
              Return to Knowledge Base
            </Link>
          </Box>
        </Paper>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Add the CSS styles */}
      <style>{markdownStyles}</style>
      
      {/* Breadcrumbs navigation */}
      <Breadcrumbs sx={{ mb: 2 }}>
        <Link component={RouterLink} to="/" color="inherit">
          Home
        </Link>
        <Link component={RouterLink} to="/knowledge-base" color="inherit">
          Knowledge Base
        </Link>
        <Typography color="text.primary">{tutorialOption.name}</Typography>
      </Breadcrumbs>

      {/* Tutorial header */}
      <Paper
        elevation={0}
        sx={{
          p: 4,
          mb: 4,
          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.info.main, 0.1)} 100%)`,
          borderRadius: 2,
          display: 'flex',
          alignItems: 'center',
          gap: 2,
        }}
      >
        <Box
          sx={{
            bgcolor: tutorialOption.color,
            width: 56,
            height: 56,
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
          }}
        >
          {tutorialOption.icon}
        </Box>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
            {tutorialOption.name}
          </Typography>
          <Typography variant="body1" color="text.secondary">
            {tutorialOption.shortDescription}
          </Typography>
        </Box>
      </Paper>

      {/* Tutorial content */}
      <Paper sx={{ p: 4, borderRadius: 2 }}>
        <Box className="markdown-content" dangerouslySetInnerHTML={{ __html: processedContent }} /> {/* Render processed HTML */}
      </Paper>
    </Container>
  );
};

export default TutorialPage;
