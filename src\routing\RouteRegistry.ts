// Route registry for managing application routes

import { RouteConfig, RouteRegistry } from '../types/routing';

class RouteRegistryImpl implements RouteRegistry {
  private routes: Map<string, RouteConfig> = new Map();
  private routeTree: RouteConfig[] = [];

  /**
   * Register new routes
   */
  register(routes: RouteConfig[]): void {
    routes.forEach(route => {
      this.routes.set(route.path, route);
      this.addToTree(route);

      // Register children if they exist
      if (route.children) {
        route.children.forEach(child => {
          this.routes.set(child.path, child);
          this.addToTree(child);
        });
      }
    });
  }

  /**
   * Unregister routes by paths
   */
  unregister(paths: string[]): void {
    paths.forEach(path => {
      this.routes.delete(path);
      this.removeFromTree(path);
    });
  }

  /**
   * Get a specific route by path
   */
  getRoute(path: string): RouteConfig | undefined {
    return this.routes.get(path);
  }

  /**
   * Get all registered routes
   */
  getAllRoutes(): RouteConfig[] {
    return Array.from(this.routes.values());
  }

  /**
   * Find matching route based on page and subPage
   */
  findMatchingRoute(page: string, subPage?: string): RouteConfig | undefined {
    // First try exact match with subpage
    if (subPage) {
      const fullPath = `${page}/${subPage}`;
      const exactMatch = this.routes.get(fullPath);
      if (exactMatch) {
        return exactMatch;
      }
    }

    // Then try page-level match
    const pageMatch = this.routes.get(page);
    if (pageMatch) {
      // If it has children, try to find subpage match
      if (pageMatch.children && subPage) {
        const childMatch = pageMatch.children.find(child =>
          child.path === subPage || child.path === `${page}/${subPage}`
        );
        if (childMatch) {
          return childMatch;
        }
      }
      return pageMatch;
    }

    // Try partial matches for nested routes
    for (const [path, route] of this.routes) {
      if (path.startsWith(page + '/')) {
        const pathParts = path.split('/');
        if (pathParts.length === 2 && pathParts[1] === subPage) {
          return route;
        }
      }
    }

    // Debug logging only in development when no route is found
    if (process.env.NODE_ENV === 'development') {
      console.warn(`❌ No route found for page: "${page}", subPage: "${subPage}"`);
      console.log(`Available routes:`, Array.from(this.routes.keys()).slice(0, 20));
    }

    return undefined;
  }

  /**
   * Get routes by category
   */
  getRoutesByCategory(category: string): RouteConfig[] {
    return this.getAllRoutes().filter(route => 
      (route as any).metadata?.category === category
    );
  }

  /**
   * Get public routes
   */
  getPublicRoutes(): RouteConfig[] {
    return this.getAllRoutes().filter(route => route.allowPublic);
  }

  /**
   * Get routes that require authentication
   */
  getAuthRoutes(): RouteConfig[] {
    return this.getAllRoutes().filter(route => route.requiresAuth);
  }

  /**
   * Clear all routes
   */
  clear(): void {
    this.routes.clear();
    this.routeTree = [];
  }

  private addToTree(route: RouteConfig): void {
    // Simple implementation - just add to flat array
    // Could be enhanced to build actual tree structure
    if (!this.routeTree.find(r => r.path === route.path)) {
      this.routeTree.push(route);
    }
  }

  private removeFromTree(path: string): void {
    this.routeTree = this.routeTree.filter(route => route.path !== path);
  }
}

// Singleton instance
export const routeRegistry = new RouteRegistryImpl();

// Helper functions for common operations
export function registerRoute(route: RouteConfig): void {
  routeRegistry.register([route]);
}

export function registerRoutes(routes: RouteConfig[]): void {
  routeRegistry.register(routes);
}

export function findRoute(page: string, subPage?: string): RouteConfig | undefined {
  return routeRegistry.findMatchingRoute(page, subPage);
}

export function getAllRoutes(): RouteConfig[] {
  return routeRegistry.getAllRoutes();
}
